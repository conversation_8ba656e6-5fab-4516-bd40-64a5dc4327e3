from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from django.http import HttpResponse

import csv
import openpyxl
from openpyxl.styles import Font, Alignment
from .models import Organization, Invitation, Scholar, ScholarInvitation, ElectedOfficial, FormerMinister, DiplomaticCorps, Notable


# تخصيص موقع الإدارة
admin.site.site_header = 'لوحة إدارة المؤتمر السنوي'
admin.site.site_title = 'إدارة المؤتمر'
admin.site.index_title = 'مرحباً بك في لوحة إدارة المؤتمر'

@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ('name', 'email', 'contact_person', 'organization_type', 'participation_status', 'view_links', 'created_at')
    list_filter = ('participation_status', 'organization_type', 'created_at')
    search_fields = ('name', 'name_fr', 'email', 'contact_person', 'description')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('name', 'name_fr', 'email', 'phone', 'contact_person', 'website')
        }),
        (_('Address & Location'), {
            'fields': ('address', 'latitude', 'longitude')
        }),
        (_('Organization Details'), {
            'fields': ('organization_type', 'participation_status', 'description', 'logo')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def view_links(self, obj):
        """Add quick links to view organization details"""
        detail_url = reverse('organizations:organization_detail', args=[obj.pk])
        return format_html(
            '<a href="{}" target="_blank" class="button">عرض التفاصيل</a>',
            detail_url
        )
    view_links.short_description = _('Quick Links')

    actions = ['mark_as_confirmed', 'mark_as_attended', 'mark_as_declined']

    def mark_as_confirmed(self, request, queryset):
        updated = queryset.update(participation_status='confirmed')
        self.message_user(request, f'{updated} organizations marked as confirmed.')
    mark_as_confirmed.short_description = _('Mark selected organizations as confirmed')

    def mark_as_attended(self, request, queryset):
        updated = queryset.update(participation_status='attended')
        self.message_user(request, f'{updated} organizations marked as attended.')
    mark_as_attended.short_description = _('Mark selected organizations as attended')

    def mark_as_declined(self, request, queryset):
        updated = queryset.update(participation_status='declined')
        self.message_user(request, f'{updated} organizations marked as declined.')
    mark_as_declined.short_description = _('Mark selected organizations as declined')

@admin.register(Invitation)
class InvitationAdmin(admin.ModelAdmin):
    list_display = ('organization', 'subject', 'sent_at', 'is_sent', 'status', 'response_status')
    list_filter = ('is_sent', 'status', 'response_status', 'sent_at')
    search_fields = ('subject', 'message', 'organization__name')
    date_hierarchy = 'sent_at'
    raw_id_fields = ('organization',)
    readonly_fields = ('sent_at', 'updated_at')

    fieldsets = (
        (_('Invitation Details'), {
            'fields': ('organization', 'subject', 'message')
        }),
        (_('Status'), {
            'fields': ('status', 'is_sent', 'response_status', 'response_date')
        }),
        (_('Timestamps'), {
            'fields': ('sent_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Scholar)
class ScholarAdmin(admin.ModelAdmin):
    list_display = ('get_full_title_name', 'position', 'organization', 'country', 'participation_status', 'created_at')
    list_filter = ('title', 'participation_status', 'country', 'created_at')
    search_fields = ('name', 'full_name', 'position', 'organization', 'email')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('المعلومات الأساسية'), {
            'fields': ('title', 'name', 'full_name', 'photo')
        }),
        (_('المعلومات المهنية'), {
            'fields': ('position', 'organization', 'specialization')
        }),
        (_('المعلومات الجغرافية'), {
            'fields': ('country', 'city')
        }),
        (_('معلومات الاتصال'), {
            'fields': ('email', 'phone', 'website')
        }),
        (_('المشاركة'), {
            'fields': ('participation_status',)
        }),

        (_('معلومات إضافية'), {
            'fields': ('biography',),
            'classes': ('collapse',)
        }),
        (_('معلومات النظام'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['export_to_excel', 'export_to_csv', 'mark_as_confirmed', 'mark_as_attended', 'mark_as_declined']

    def export_to_excel(self, request, queryset):
        """تصدير العلماء إلى ملف Excel"""
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="scholars.xlsx"'

        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = 'العلماء'

        # إعداد العناوين
        headers = [
            'الاسم', 'الوظيفة', 'المؤسسة', 'البلد', 'الهاتف', 'البريد الإلكتروني'
        ]

        # كتابة العناوين
        for col_num, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.value = header
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # كتابة البيانات
        for row_num, scholar in enumerate(queryset, 2):
            worksheet.cell(row=row_num, column=1, value=scholar.get_full_title_name())
            worksheet.cell(row=row_num, column=2, value=scholar.position or '')
            worksheet.cell(row=row_num, column=3, value=scholar.organization or '')
            worksheet.cell(row=row_num, column=4, value=scholar.country or '')
            worksheet.cell(row=row_num, column=5, value=scholar.phone or '')
            worksheet.cell(row=row_num, column=6, value=scholar.email or '')

        workbook.save(response)
        return response

    export_to_excel.short_description = _('تصدير إلى Excel')

    def export_to_csv(self, request, queryset):
        """تصدير العلماء إلى ملف CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="scholars.csv"'
        response.write('\ufeff')  # BOM for UTF-8

        writer = csv.writer(response)
        writer.writerow(['الاسم الكامل', 'الوظيفة', 'المؤسسة', 'البلد', 'الهاتف', 'البريد الإلكتروني', 'حالة المشاركة'])

        for scholar in queryset:
            writer.writerow([
                scholar.get_full_title_name(),
                scholar.position or '',
                scholar.organization or '',
                scholar.country or '',
                scholar.phone or '',
                scholar.email or '',
                scholar.get_participation_status_display()
            ])

        return response

    export_to_csv.short_description = _('تصدير إلى CSV')

    def mark_as_confirmed(self, request, queryset):
        updated = queryset.update(participation_status='confirmed')
        self.message_user(request, f'{updated} عالم تم تأكيد مشاركتهم.')
    mark_as_confirmed.short_description = _('تأكيد مشاركة العلماء المحددين')

    def mark_as_attended(self, request, queryset):
        updated = queryset.update(participation_status='attended')
        self.message_user(request, f'{updated} عالم تم تسجيل حضورهم.')
    mark_as_attended.short_description = _('تسجيل حضور العلماء المحددين')

    def mark_as_declined(self, request, queryset):
        updated = queryset.update(participation_status='declined')
        self.message_user(request, f'{updated} عالم تم تسجيل اعتذارهم.')
    mark_as_declined.short_description = _('تسجيل اعتذار العلماء المحددين')


@admin.register(ScholarInvitation)
class ScholarInvitationAdmin(admin.ModelAdmin):
    list_display = ('scholar', 'subject', 'sent_at', 'is_sent', 'status', 'response_status')
    list_filter = ('is_sent', 'status', 'response_status', 'sent_at')
    search_fields = ('subject', 'message', 'scholar__name', 'scholar__full_name')
    date_hierarchy = 'sent_at'
    raw_id_fields = ('scholar',)
    readonly_fields = ('sent_at', 'updated_at')

    fieldsets = (
        (_('تفاصيل الدعوة'), {
            'fields': ('scholar', 'subject', 'message')
        }),
        (_('الحالة'), {
            'fields': ('status', 'is_sent', 'response_status', 'response_date')
        }),
        (_('التوقيتات'), {
            'fields': ('sent_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('scholar')


@admin.register(ElectedOfficial)
class ElectedOfficialAdmin(admin.ModelAdmin):
    list_display = ('name', 'position', 'region', 'party', 'status', 'phone', 'start_date', 'end_date')
    list_filter = ('position', 'status', 'start_date', 'party')
    search_fields = ('name', 'region', 'party', 'phone', 'email')
    list_editable = ('status',)
    date_hierarchy = 'start_date'

    fieldsets = (
        (_('المعلومات الأساسية'), {
            'fields': ('name', 'position', 'region', 'party')
        }),
        (_('معلومات الاتصال'), {
            'fields': ('phone', 'email')
        }),
        (_('تفاصيل المنصب'), {
            'fields': ('status', 'start_date', 'end_date', 'notes')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request)

    actions = ['export_elected_to_excel', 'mark_as_active', 'mark_as_inactive']

    def export_elected_to_excel(self, request, queryset):
        """تصدير المنتخبين إلى ملف Excel"""
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="elected_officials.xlsx"'

        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = 'المنتخبون'

        # إعداد العناوين
        headers = ['الاسم', 'المنصب', 'المنطقة', 'الحزب', 'الهاتف', 'البريد الإلكتروني', 'الحالة', 'تاريخ البداية', 'تاريخ النهاية', 'ملاحظات']
        for col_num, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.value = header
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # إضافة البيانات
        for row_num, official in enumerate(queryset, 2):
            worksheet.cell(row=row_num, column=1, value=official.name)
            worksheet.cell(row=row_num, column=2, value=official.get_position_display())
            worksheet.cell(row=row_num, column=3, value=official.region)
            worksheet.cell(row=row_num, column=4, value=official.party or '')
            worksheet.cell(row=row_num, column=5, value=official.phone or '')
            worksheet.cell(row=row_num, column=6, value=official.email or '')
            worksheet.cell(row=row_num, column=7, value=official.get_status_display())
            worksheet.cell(row=row_num, column=8, value=official.start_date.strftime('%Y-%m-%d') if official.start_date else '')
            worksheet.cell(row=row_num, column=9, value=official.end_date.strftime('%Y-%m-%d') if official.end_date else '')
            worksheet.cell(row=row_num, column=10, value=official.notes or '')

        workbook.save(response)
        return response

    export_elected_to_excel.short_description = _('تصدير المنتخبين إلى Excel')

    def mark_as_active(self, request, queryset):
        """تحديد المنتخبين كنشطين"""
        updated = queryset.update(status='active')
        self.message_user(request, f'تم تحديث {updated} منتخب كنشط.')

    mark_as_active.short_description = _('تحديد كنشط')

    def mark_as_inactive(self, request, queryset):
        """تحديد المنتخبين كغير نشطين"""
        updated = queryset.update(status='inactive')
        self.message_user(request, f'تم تحديث {updated} منتخب كغير نشط.')

    mark_as_inactive.short_description = _('تحديد كغير نشط')


@admin.register(FormerMinister)
class FormerMinisterAdmin(admin.ModelAdmin):
    list_display = ('name', 'ministry', 'government', 'start_date', 'end_date', 'get_service_duration', 'status', 'current_position')
    list_filter = ('ministry', 'government', 'status', 'start_date')
    search_fields = ('name', 'ministry', 'government', 'current_position')
    date_hierarchy = 'start_date'
    ordering = ['-start_date', 'name']

    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('name', 'ministry', 'government')
        }),
        ('فترة الخدمة', {
            'fields': ('start_date', 'end_date', 'status')
        }),
        ('معلومات الاتصال', {
            'fields': ('phone', 'email')
        }),
        ('المعلومات الحالية', {
            'fields': ('current_position',)
        }),
        ('تفاصيل إضافية', {
            'fields': ('achievements', 'notes'),
            'classes': ('collapse',)
        }),
    )

    actions = ['export_ministers_to_excel', 'mark_as_active', 'mark_as_inactive', 'mark_as_deceased']

    def export_ministers_to_excel(self, request, queryset):
        """تصدير الوزراء السابقين إلى ملف Excel"""
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="former_ministers.xlsx"'

        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = 'الوزراء السابقون'

        # إعداد العناوين
        headers = ['الاسم', 'الوزارة', 'الحكومة', 'تاريخ البداية', 'تاريخ النهاية', 'مدة الخدمة', 'الحالة', 'المنصب الحالي', 'الإنجازات']
        for col_num, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.value = header
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # إضافة البيانات
        for row_num, minister in enumerate(queryset, 2):
            worksheet.cell(row=row_num, column=1, value=minister.name)
            worksheet.cell(row=row_num, column=2, value=minister.ministry)
            worksheet.cell(row=row_num, column=3, value=minister.government or '')
            worksheet.cell(row=row_num, column=4, value=minister.start_date.strftime('%Y-%m-%d') if minister.start_date else '')
            worksheet.cell(row=row_num, column=5, value=minister.end_date.strftime('%Y-%m-%d') if minister.end_date else '')
            worksheet.cell(row=row_num, column=6, value=minister.get_service_duration())
            worksheet.cell(row=row_num, column=7, value=minister.get_status_display())
            worksheet.cell(row=row_num, column=8, value=minister.current_position or '')
            worksheet.cell(row=row_num, column=9, value=minister.achievements or '')

        workbook.save(response)
        return response

    export_ministers_to_excel.short_description = _('تصدير إلى Excel')

    def mark_as_active(self, request, queryset):
        """تحديد الوزراء كنشطين"""
        updated = queryset.update(status='active')
        self.message_user(request, f'تم تحديث {updated} وزير كنشط.')

    mark_as_active.short_description = _('تحديد كنشط')

    def mark_as_inactive(self, request, queryset):
        """تحديد الوزراء كغير نشطين"""
        updated = queryset.update(status='inactive')
        self.message_user(request, f'تم تحديث {updated} وزير كغير نشط.')

    mark_as_inactive.short_description = _('تحديد كغير نشط')

    def mark_as_deceased(self, request, queryset):
        """تحديد الوزراء كمتوفين"""
        updated = queryset.update(status='deceased')
        self.message_user(request, f'تم تحديث {updated} وزير كمتوفى.')

    mark_as_deceased.short_description = _('تحديد كمتوفى')


@admin.register(DiplomaticCorps)
class DiplomaticCorpsAdmin(admin.ModelAdmin):
    list_display = ('name', 'rank', 'country', 'mission_type', 'start_date', 'end_date', 'get_service_duration', 'status')
    list_filter = ('rank', 'mission_type', 'status', 'country', 'start_date')
    search_fields = ('name', 'country', 'current_position', 'languages')
    date_hierarchy = 'start_date'
    ordering = ['-start_date', 'name']

    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('name', 'rank', 'country', 'mission_type')
        }),
        ('فترة الخدمة', {
            'fields': ('start_date', 'end_date', 'status')
        }),
        ('معلومات الاتصال', {
            'fields': ('phone', 'email')
        }),
        ('المعلومات المهنية', {
            'fields': ('current_position', 'languages', 'education')
        }),
        ('تفاصيل إضافية', {
            'fields': ('achievements', 'notes'),
            'classes': ('collapse',)
        }),
    )

    actions = ['export_diplomatic_to_excel', 'mark_as_active', 'mark_as_inactive', 'mark_as_retired']

    def export_diplomatic_to_excel(self, request, queryset):
        """تصدير السلك الدبلوماسي إلى ملف Excel"""
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="diplomatic_corps.xlsx"'

        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = 'السلك الدبلوماسي'

        # إعداد العناوين
        headers = ['الاسم', 'الرتبة', 'البلد/المهمة', 'نوع المهمة', 'تاريخ البداية', 'تاريخ النهاية', 'مدة الخدمة', 'الحالة', 'المنصب الحالي', 'اللغات']
        for col_num, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.value = header
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # إضافة البيانات
        for row_num, diplomat in enumerate(queryset, 2):
            worksheet.cell(row=row_num, column=1, value=diplomat.name)
            worksheet.cell(row=row_num, column=2, value=diplomat.get_rank_display())
            worksheet.cell(row=row_num, column=3, value=diplomat.country)
            worksheet.cell(row=row_num, column=4, value=diplomat.get_mission_type_display())
            worksheet.cell(row=row_num, column=5, value=diplomat.start_date.strftime('%Y-%m-%d') if diplomat.start_date else '')
            worksheet.cell(row=row_num, column=6, value=diplomat.end_date.strftime('%Y-%m-%d') if diplomat.end_date else '')
            worksheet.cell(row=row_num, column=7, value=diplomat.get_service_duration())
            worksheet.cell(row=row_num, column=8, value=diplomat.get_status_display())
            worksheet.cell(row=row_num, column=9, value=diplomat.current_position or '')
            worksheet.cell(row=row_num, column=10, value=diplomat.languages or '')

        workbook.save(response)
        return response

    export_diplomatic_to_excel.short_description = _('تصدير إلى Excel')

    def mark_as_active(self, request, queryset):
        """تحديد أعضاء السلك الدبلوماسي كنشطين"""
        updated = queryset.update(status='active')
        self.message_user(request, f'تم تحديث {updated} عضو كنشط.')

    mark_as_active.short_description = _('تحديد كنشط')

    def mark_as_inactive(self, request, queryset):
        """تحديد أعضاء السلك الدبلوماسي كغير نشطين"""
        updated = queryset.update(status='inactive')
        self.message_user(request, f'تم تحديث {updated} عضو كغير نشط.')

    mark_as_inactive.short_description = _('تحديد كغير نشط')

    def mark_as_retired(self, request, queryset):
        """تحديد أعضاء السلك الدبلوماسي كمتقاعدين"""
        updated = queryset.update(status='retired')
        self.message_user(request, f'تم تحديث {updated} عضو كمتقاعد.')

    mark_as_retired.short_description = _('تحديد كمتقاعد')


@admin.register(Notable)
class NotableAdmin(admin.ModelAdmin):
    list_display = ('get_full_title', 'category', 'region', 'tribe', 'get_age', 'status')
    list_filter = ('category', 'region', 'status', 'tribe')
    search_fields = ('name', 'title', 'tribe', 'current_position', 'achievements')
    ordering = ['name']

    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('name', 'title', 'category', 'birth_year')
        }),
        ('المعلومات الجغرافية والقبلية', {
            'fields': ('region', 'tribe', 'address')
        }),
        ('معلومات الاتصال', {
            'fields': ('phone', 'email')
        }),
        ('المعلومات المهنية والاجتماعية', {
            'fields': ('current_position', 'status', 'social_influence')
        }),
        ('التعليم والإنجازات', {
            'fields': ('education', 'achievements')
        }),
        ('معلومات إضافية', {
            'fields': ('family_info', 'notes'),
            'classes': ('collapse',)
        }),
    )

    actions = ['export_notables_to_excel', 'mark_as_active', 'mark_as_inactive', 'mark_as_deceased']

    def export_notables_to_excel(self, request, queryset):
        """تصدير الأعيان إلى ملف Excel"""
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="notables.xlsx"'

        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = 'الأعيان'

        # إعداد العناوين
        headers = ['الاسم الكامل', 'اللقب', 'الفئة', 'المنطقة', 'القبيلة', 'العمر', 'الحالة', 'المنصب الحالي', 'الهاتف', 'البريد الإلكتروني']
        for col_num, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.value = header
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # إضافة البيانات
        for row_num, notable in enumerate(queryset, 2):
            worksheet.cell(row=row_num, column=1, value=notable.name)
            worksheet.cell(row=row_num, column=2, value=notable.title or '')
            worksheet.cell(row=row_num, column=3, value=notable.get_category_display())
            worksheet.cell(row=row_num, column=4, value=notable.get_region_display())
            worksheet.cell(row=row_num, column=5, value=notable.tribe or '')
            worksheet.cell(row=row_num, column=6, value=notable.get_age() or '')
            worksheet.cell(row=row_num, column=7, value=notable.get_status_display())
            worksheet.cell(row=row_num, column=8, value=notable.current_position or '')
            worksheet.cell(row=row_num, column=9, value=notable.phone or '')
            worksheet.cell(row=row_num, column=10, value=notable.email or '')

        workbook.save(response)
        return response

    export_notables_to_excel.short_description = _('تصدير إلى Excel')

    def mark_as_active(self, request, queryset):
        """تحديد الأعيان كنشطين"""
        updated = queryset.update(status='active')
        self.message_user(request, f'تم تحديث {updated} عين كنشط.')

    mark_as_active.short_description = _('تحديد كنشط')

    def mark_as_inactive(self, request, queryset):
        """تحديد الأعيان كغير نشطين"""
        updated = queryset.update(status='inactive')
        self.message_user(request, f'تم تحديث {updated} عين كغير نشط.')

    mark_as_inactive.short_description = _('تحديد كغير نشط')

    def mark_as_deceased(self, request, queryset):
        """تحديد الأعيان كمتوفين"""
        updated = queryset.update(status='deceased')
        self.message_user(request, f'تم تحديث {updated} عين كمتوفى.')

    mark_as_deceased.short_description = _('تحديد كمتوفى')