from django import forms
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from core.decorators import regular_admin_required, filter_data_by_admin, get_admin_context
from django.contrib.admin.views.decorators import staff_member_required
from django.core.mail import send_mail
from django.db.models import Q, Count
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST
import xlwt
import openpyxl
from openpyxl.styles import Font, Alignment
import json
from datetime import datetime

from .models import Organization, Invitation, Scholar, ScholarInvitation, ElectedOfficial, FormerMinister, DiplomaticCorps, Notable, PartyLeader
from .forms import OrganizationForm, InvitationForm, InvitationUpdateForm, BulkInvitationForm, ContactForm, OrganizationFilterForm, ScholarInvitationForm, BulkScholarInvitationForm, ElectedOfficialForm, FormerMinisterForm, DiplomaticCorpsForm, NotableForm, PartyLeaderForm
import urllib.parse


def create_whatsapp_link(phone_number, message):
    """
    إنشاء رابط WhatsApp مع رقم الهاتف والرسالة
    """
    # تنظيف رقم الهاتف
    clean_phone = phone_number.replace('+', '').replace('-', '').replace(' ', '')

    # ترميز الرسالة للـ URL
    encoded_message = urllib.parse.quote(message)

    # إنشاء رابط WhatsApp
    whatsapp_url = f"https://wa.me/{clean_phone}?text={encoded_message}"

    return whatsapp_url


@staff_member_required
def custom_admin_dashboard(request):
    """لوحة إدارة مخصصة للمؤتمر"""

    # إحصائيات المؤسسات
    total_organizations = Organization.objects.count()
    confirmed_organizations = Organization.objects.filter(participation_status='confirmed').count()

    # إحصائيات العلماء
    total_scholars = Scholar.objects.count()
    confirmed_scholars = Scholar.objects.filter(participation_status='confirmed').count()

    # إحصائيات الدعوات
    total_invitations = Invitation.objects.count() + ScholarInvitation.objects.count()
    sent_invitations = Invitation.objects.filter(is_sent=True).count() + ScholarInvitation.objects.filter(is_sent=True).count()

    # المشاركات المؤكدة
    confirmed_participants = confirmed_organizations + confirmed_scholars

    context = {
        'title': 'لوحة إدارة المؤتمر',
        'total_organizations': total_organizations,
        'confirmed_organizations': confirmed_organizations,
        'total_scholars': total_scholars,
        'confirmed_scholars': confirmed_scholars,
        'total_invitations': total_invitations,
        'sent_invitations': sent_invitations,
        'confirmed_participants': confirmed_participants,
    }

    return render(request, 'admin/custom_admin_dashboard.html', context)

@staff_member_required
def organization_list(request):
    """List all organizations with filtering and sorting"""
    # Get all organizations
    organizations = Organization.objects.all()

    # Filter by status if provided
    status_filter = request.GET.get('status', '')
    if status_filter:
        organizations = organizations.filter(participation_status=status_filter)

    # Sort by field if provided
    sort_by = request.GET.get('sort', 'name')
    if sort_by == 'status':
        organizations = organizations.order_by('participation_status')
    elif sort_by == 'date':
        organizations = organizations.order_by('-created_at')
    else:  # Default to name
        organizations = organizations.order_by('name')

    # Separate organizations by type
    government_organizations = organizations.filter(organization_type='government')
    private_organizations = organizations.filter(organization_type='private')
    scholar_organizations = organizations.filter(organization_type='scholar')

    context = {
        'title': _('Organizations'),
        'government_organizations': government_organizations,
        'private_organizations': private_organizations,
        'scholar_organizations': scholar_organizations,
        'current_status': status_filter,
        'current_sort': sort_by,
    }
    return render(request, 'organizations/organization_list.html', context)

@staff_member_required
def government_organizations(request):
    """List all government organizations"""
    organizations = Organization.objects.filter(organization_type='government')

    # Filter by status if provided
    status_filter = request.GET.get('status', '')
    if status_filter:
        organizations = organizations.filter(participation_status=status_filter)

    # Sort by field if provided
    sort_by = request.GET.get('sort', 'name')
    if sort_by == 'status':
        organizations = organizations.order_by('participation_status')
    elif sort_by == 'date':
        organizations = organizations.order_by('-created_at')
    else:  # Default to name
        organizations = organizations.order_by('name')

    context = {
        'title': _('Government Organizations'),
        'organizations': organizations,
        'current_status': status_filter,
        'current_sort': sort_by,
    }
    return render(request, 'organizations/government_organizations.html', context)

@staff_member_required
def consulting_organizations(request):
    """List all MS-Consulting organizations"""
    # إنشاء مؤسسة تجريبية إذا لم تكن موجودة
    if not Organization.objects.filter(organization_type='private').exists():
        Organization.objects.create(
            name="مؤسسة الاستشارات التقنية",
            email="<EMAIL>",
            phone="0123456789",
            address="الجزائر العاصمة",
            description="مؤسسة متخصصة في الاستشارات التقنية",
            organization_type="private"
        )

    organizations = Organization.objects.filter(organization_type='private')

    # Filter by status if provided
    status_filter = request.GET.get('status', '')
    if status_filter:
        organizations = organizations.filter(participation_status=status_filter)

    # Sort by field if provided
    sort_by = request.GET.get('sort', 'name')
    if sort_by == 'status':
        organizations = organizations.order_by('participation_status')
    elif sort_by == 'date':
        organizations = organizations.order_by('-created_at')
    else:  # Default to name
        organizations = organizations.order_by('name')

    context = {
        'title': _('MS-Consulting Organizations'),
        'organizations': organizations,
        'current_status': status_filter,
        'current_sort': sort_by,
    }
    return render(request, 'organizations/consulting_organizations.html', context)

@login_required
def organization_create(request):
    """Create a new organization"""
    if request.method == 'POST':
        form = OrganizationForm(request.POST, request.FILES)
        if form.is_valid():
            form.save()
            messages.success(request, _('Organization created successfully.'))
            return redirect('organizations:organization_list')
    else:
        form = OrganizationForm()

    context = {
        'title': _('Create Organization'),
        'form': form,
    }
    return render(request, 'organizations/organization_form.html', context)

def organization_detail(request, pk):
    """View organization details"""
    organization = get_object_or_404(Organization, pk=pk)
    invitations = organization.invitations.all()

    context = {
        'title': organization.name,
        'organization': organization,
        'invitations': invitations,
    }
    return render(request, 'organizations/organization_detail.html', context)

@login_required
def organization_update(request, pk):
    """Update an organization"""
    organization = get_object_or_404(Organization, pk=pk)

    if request.method == 'POST':
        form = OrganizationForm(request.POST, request.FILES, instance=organization)
        if form.is_valid():
            form.save()
            messages.success(request, _('Organization updated successfully.'))
            return redirect('organizations:organization_detail', pk=organization.pk)
    else:
        form = OrganizationForm(instance=organization)

    context = {
        'title': _('Update Organization'),
        'form': form,
        'organization': organization,
    }
    return render(request, 'organizations/organization_form.html', context)

@login_required
def organization_delete(request, pk):
    """Delete an organization"""
    organization = get_object_or_404(Organization, pk=pk)

    if request.method == 'POST':
        organization.delete()
        messages.success(request, _('Organization deleted successfully.'))
        return redirect('organizations:organization_list')

    context = {
        'title': _('Delete Organization'),
        'organization': organization,
    }
    return render(request, 'organizations/organization_confirm_delete.html', context)

@login_required
def change_organization_status(request, pk):
    """Change the participation status of an organization"""
    if request.method == 'POST':
        organization = get_object_or_404(Organization, pk=pk)
        new_status = request.POST.get('status')

        if new_status in dict(Organization.PARTICIPATION_CHOICES).keys():
            organization.participation_status = new_status
            organization.save()

            # Return JSON response for AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'status': new_status,
                    'status_display': dict(Organization.PARTICIPATION_CHOICES)[new_status]
                })

            messages.success(request, _('Organization status updated successfully.'))
            return redirect('organizations:organization_detail', pk=pk)

    # If not a POST request or invalid status
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'success': False}, status=400)

    messages.error(request, _('Invalid request.'))
    return redirect('organizations:organization_detail', pk=pk)

def contact_organization(request, pk):
    """Contact form for an organization"""
    organization = get_object_or_404(Organization, pk=pk)

    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            # Process the form data
            subject = form.cleaned_data['subject']
            message = form.cleaned_data['message']
            from_email = form.cleaned_data['email']

            # Send email to the organization
            send_mail(
                subject,
                f"Message from: {form.cleaned_data['name']} ({from_email})\n\n{message}",
                from_email,
                [organization.email],
                fail_silently=False,
            )

            messages.success(request, _('Your message has been sent successfully.'))
            return redirect('organizations:organization_detail', pk=pk)
    else:
        form = ContactForm()

    context = {
        'title': _('Contact') + f" {organization.name}",
        'organization': organization,
        'form': form,
    }
    return render(request, 'organizations/contact_organization.html', context)

def organization_map(request):
    """Display a map of all organizations"""
    organizations = Organization.objects.all()

    # Filter by status if provided
    status_filter = request.GET.get('status', '')
    if status_filter:
        organizations = organizations.filter(participation_status=status_filter)

    context = {
        'title': _('Organizations Map'),
        'organizations': organizations,
        'current_status': status_filter,
    }
    return render(request, 'organizations/organization_map.html', context)

def export_organizations_excel(request):
    """Export organizations to Excel file"""
    response = HttpResponse(content_type='application/ms-excel')
    response['Content-Disposition'] = f'attachment; filename="organizations_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xls"'

    wb = xlwt.Workbook(encoding='utf-8')
    ws = wb.add_sheet('المؤسسات')

    # Sheet header, first row
    row_num = 0

    font_style = xlwt.XFStyle()
    font_style.font.bold = True

    columns = ['الاسم', 'العنوان', 'البريد الإلكتروني', 'الهاتف', 'جهة الاتصال', 'الموقع الإلكتروني', 'الوصف', 'حالة المشاركة', 'نوع المؤسسة']

    for col_num in range(len(columns)):
        ws.write(row_num, col_num, columns[col_num], font_style)

    # Sheet body, remaining rows
    font_style = xlwt.XFStyle()

    organizations = Organization.objects.all().order_by('name')

    # Create a dictionary of status display values (converting lazy translation objects to strings)
    participation_status_dict = {}
    for key, value in Organization.PARTICIPATION_CHOICES:
        participation_status_dict[key] = str(value)

    # Create a dictionary of organization type display values (converting lazy translation objects to strings)
    organization_type_dict = {}
    for key, value in Organization.ORGANIZATION_TYPE_CHOICES:
        organization_type_dict[key] = str(value)

    for organization in organizations:
        row_num += 1

        # Get display value for participation status and organization type
        participation_status_display = participation_status_dict.get(organization.participation_status, '')
        organization_type_display = organization_type_dict.get(organization.organization_type, '')

        # Ensure all values are properly converted to strings to avoid lazy translation issues
        row = [
            str(organization.name),
            str(organization.address) if organization.address else '',
            str(organization.email),
            str(organization.phone) if organization.phone else '',
            str(organization.contact_person) if organization.contact_person else '',
            str(organization.website) if organization.website else '',
            str(organization.description) if organization.description else '',
            str(participation_status_display),
            str(organization_type_display)
        ]

        for col_num in range(len(row)):
            ws.write(row_num, col_num, row[col_num], font_style)

    wb.save(response)
    return response

def send_invitation_email(request, organization, subject, message):
    """
    Helper function to send invitation email to an organization
    """
    # إنشاء سجل الدعوة
    invitation = Invitation.objects.create(
        organization=organization,
        subject=subject,
        message=message,
        is_sent=True,
        status='sent'
    )

    # إرسال البريد الإلكتروني
    try:
        # الحصول على عنوان البريد الإلكتروني للمؤسسة
        to_email = organization.email

        # إرسال البريد الإلكتروني
        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[to_email],
            fail_silently=False,
        )

        return invitation
    except Exception as e:
        # تحديث حالة الدعوة في حالة فشل الإرسال
        invitation.is_sent = False
        invitation.status = 'failed'
        invitation.save()
        raise e


def send_scholar_invitation_email(request, scholar, subject, message):
    """
    Helper function to send invitation email to a scholar
    """
    # إنشاء سجل الدعوة
    invitation = ScholarInvitation.objects.create(
        scholar=scholar,
        subject=subject,
        message=message,
        is_sent=True,
        status='sent'
    )

    # إرسال البريد الإلكتروني
    try:
        # الحصول على عنوان البريد الإلكتروني للعالم
        to_email = scholar.email

        if not to_email:
            raise ValueError(f"لا يوجد بريد إلكتروني للعالم {scholar.get_full_title_name()}")

        # إرسال البريد الإلكتروني
        print(f"DEBUG: Sending email to {to_email} with subject: {subject}")

        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[to_email],
            fail_silently=False,
        )

        print(f"DEBUG: Email sent successfully to {to_email}")
        return invitation

    except Exception as e:
        # تحديث حالة الدعوة في حالة فشل الإرسال
        invitation.is_sent = False
        invitation.status = 'failed'
        invitation.save()
        print(f"ERROR: Failed to send email to {to_email}: {str(e)}")
        raise e


def format_phone_number(phone):
    """
    تنسيق رقم الهاتف ليكون متوافقاً مع WhatsApp
    """
    if not phone:
        return None

    # إزالة جميع الرموز والمسافات
    phone = ''.join(filter(str.isdigit, phone))

    # إضافة رمز الدولة إذا لم يكن موجوداً
    if phone.startswith('0'):
        phone = '213' + phone[1:]  # رمز الجزائر
    elif not phone.startswith('213'):
        phone = '213' + phone

    return phone


def send_scholar_invitation_whatsapp(request, scholar, subject, message):
    """
    Helper function to send invitation via WhatsApp to a scholar
    """
    # إنشاء سجل الدعوة
    invitation = ScholarInvitation.objects.create(
        scholar=scholar,
        subject=subject,
        message=message,
        is_sent=True,
        status='sent'
    )

    try:
        # الحصول على رقم الهاتف
        phone = format_phone_number(scholar.phone)

        if not phone:
            raise ValueError(f"لا يوجد رقم هاتف للعالم {scholar.get_full_title_name()}")

        # إعداد نص الرسالة لـ WhatsApp
        whatsapp_message = f"{subject}\n\n{message}"

        # حفظ رقم الهاتف والرسالة في الدعوة للمراجعة
        invitation.phone_number = phone
        invitation.whatsapp_message = whatsapp_message

        # إنشاء رابط WhatsApp
        import urllib.parse
        encoded_message = urllib.parse.quote(whatsapp_message)
        whatsapp_url = f"https://wa.me/{phone}?text={encoded_message}"
        invitation.whatsapp_url = whatsapp_url

        invitation.save()

        return invitation

    except Exception as e:
        # تحديث حالة الدعوة في حالة فشل الإرسال
        invitation.is_sent = False
        invitation.status = 'failed'
        invitation.save()
        raise e

def send_invitation(request):
    """Send invitation to a single organization"""
    if request.method == 'POST':
        form = InvitationForm(request.POST)
        if form.is_valid():
            organization = form.cleaned_data['organization']
            subject = form.cleaned_data['subject']
            message = form.cleaned_data['message']

            try:
                # استخدام الدالة المساعدة لإرسال الدعوة
                send_invitation_email(request, organization, subject, message)
                messages.success(request, f'تم إرسال الدعوة إلى {organization.name} بنجاح!')
                # إعادة التوجيه إلى صفحة المؤسسات بدلاً من صفحة قائمة الدعوات
                return redirect('organizations:organization_list')
            except Exception as e:
                messages.error(request, f'فشل إرسال الدعوة: {str(e)}')
    else:
        # التحقق من وجود معرف مؤسسة في الطلب
        org_id = request.GET.get('organization_id')
        initial_data = {}

        if org_id and org_id.isdigit():
            try:
                organization = Organization.objects.get(id=int(org_id))
                initial_data['organization'] = organization
            except Organization.DoesNotExist:
                pass

        form = InvitationForm(initial=initial_data)

    context = {
        'form': form,
        'title': 'إرسال دعوة',
    }
    return render(request, 'organizations/invitation_form.html', context)

def send_bulk_invitation(request):
    if request.method == 'POST':
        form = BulkInvitationForm(request.POST)
        if form.is_valid():
            organizations = form.cleaned_data['organizations']
            subject = form.cleaned_data['subject']
            message = form.cleaned_data['message']

            # إرسال الدعوات
            success_count = 0
            failed_orgs = []
            for organization in organizations:
                try:
                    send_invitation_email(request, organization, subject, message)
                    success_count += 1
                except Exception as e:
                    failed_orgs.append(organization.name)
                    messages.error(request, f'فشل إرسال الدعوة إلى {organization.name}: {str(e)}')

            if success_count > 0:
                messages.success(
                    request,
                    f'تم إرسال {success_count} دعوة بنجاح من أصل {len(organizations)} مؤسسة'
                )

            if failed_orgs:
                messages.warning(
                    request,
                    f'فشل إرسال الدعوات إلى {len(failed_orgs)} مؤسسة: {"، ".join(failed_orgs[:5])}' +
                    ("..." if len(failed_orgs) > 5 else "")
                )

            # إعادة التوجيه إلى صفحة المؤسسات بدلاً من صفحة قائمة الدعوات
            return redirect('organizations:organization_list')
    else:
        # التحقق من وجود مؤسسات محددة في الطلب
        selected_orgs = request.GET.get('selected', '')
        initial_data = {}

        if selected_orgs:
            # تحويل قائمة المعرفات إلى قائمة من الأرقام الصحيحة
            org_ids = [int(org_id) for org_id in selected_orgs.split(',') if org_id.isdigit()]
            if org_ids:
                # الحصول على المؤسسات المحددة
                selected_organizations = Organization.objects.filter(id__in=org_ids)
                initial_data['organizations'] = selected_organizations

        form = BulkInvitationForm(initial=initial_data)

    return render(request, 'organizations/bulk_invitation_form.html', {'form': form})

def invitation_list(request):
    """List all invitations with search and filter capabilities"""
    invitations = Invitation.objects.all()

    # تطبيق البحث
    search_query = request.GET.get('search', '')
    if search_query:
        invitations = invitations.filter(
            Q(subject__icontains=search_query) |
            Q(message__icontains=search_query) |
            Q(organization__name__icontains=search_query)
        )

    # تطبيق التصفية حسب الحالة
    status_filter = request.GET.get('status', '')
    if status_filter:
        invitations = invitations.filter(status=status_filter)

    # تطبيق الترتيب
    sort_by = request.GET.get('sort', '-sent_at')
    invitations = invitations.order_by(sort_by)

    context = {
        'title': _('Invitations'),
        'invitations': invitations,
    }
    return render(request, 'organizations/invitation_list.html', context)

def invitation_detail(request, pk):
    """View invitation details"""
    invitation = get_object_or_404(Invitation, pk=pk)
    context = {
        'title': _('Invitation Details'),
        'invitation': invitation,
    }
    return render(request, 'organizations/invitation_detail.html', context)

@login_required
def invitation_update(request, pk):
    """Update an invitation"""
    invitation = get_object_or_404(Invitation, pk=pk)

    if request.method == 'POST':
        form = InvitationUpdateForm(request.POST, instance=invitation)
        if form.is_valid():
            form.save()
            messages.success(request, _('Invitation updated successfully.'))
            return redirect('organizations:invitation_detail', pk=invitation.pk)
    else:
        form = InvitationUpdateForm(instance=invitation)

    context = {
        'title': _('Update Invitation'),
        'form': form,
        'invitation': invitation,
    }
    return render(request, 'organizations/invitation_update_form.html', context)

@login_required
def invitation_delete(request, pk):
    """Delete an invitation"""
    invitation = get_object_or_404(Invitation, pk=pk)

    if request.method == 'POST':
        invitation.delete()
        messages.success(request, _('Invitation deleted successfully.'))
        return redirect('organizations:invitation_list')

    context = {
        'title': _('Delete Invitation'),
        'invitation': invitation,
    }
    return render(request, 'organizations/invitation_confirm_delete.html', context)

@login_required
def bulk_invitation_delete(request):
    """Delete multiple invitations at once"""
    if request.method == 'POST':
        invitation_ids = request.POST.getlist('invitation_ids')
        if invitation_ids:
            # الحصول على عدد الدعوات المحددة
            count = len(invitation_ids)

            # حذف الدعوات المحددة
            Invitation.objects.filter(id__in=invitation_ids).delete()

            # إظهار رسالة نجاح
            messages.success(
                request,
                _('%(count)s invitations deleted successfully.') % {'count': count}
            )
        else:
            messages.warning(request, _('No invitations selected.'))

    return redirect('organizations:invitation_list')

@login_required
def invitation_send(request, pk):
    """Send an invitation"""
    invitation = get_object_or_404(Invitation, pk=pk)

    # Send email immediately without confirmation page
    send_mail(
        invitation.subject,
        invitation.message,
        settings.DEFAULT_FROM_EMAIL,
        [invitation.organization.email],
        fail_silently=False,
    )

    invitation.send()
    messages.success(request, _('Invitation sent successfully.'))
    return redirect('organizations:invitation_detail', pk=invitation.pk)

@login_required
def invitation_cancel(request, pk):
    """Cancel an invitation"""
    invitation = get_object_or_404(Invitation, pk=pk)

    if request.method == 'POST':
        invitation.cancel()
        messages.success(request, _('Invitation cancelled successfully.'))
        return redirect('organizations:invitation_detail', pk=invitation.pk)

    context = {
        'title': _('Cancel Invitation'),
        'invitation': invitation,
    }
    return render(request, 'organizations/invitation_confirm_cancel.html', context)

@staff_member_required
def scholars(request):
    """List all scholar organizations"""
    organizations = Organization.objects.filter(organization_type='scholar')

    # Filter by status if provided
    status_filter = request.GET.get('status', '')
    if status_filter:
        organizations = organizations.filter(participation_status=status_filter)

    # Sort by field if provided
    sort_by = request.GET.get('sort', 'name')
    if sort_by == 'status':
        organizations = organizations.order_by('participation_status')
    elif sort_by == 'date':
        organizations = organizations.order_by('-created_at')
    else:  # Default to name
        organizations = organizations.order_by('name')

    context = {
        'title': _('Scholar Organizations'),
        'organizations': organizations,
        'current_status': status_filter,
        'current_sort': sort_by,
    }
    return render(request, 'organizations/scholars.html', context)

@staff_member_required
def government_invitations(request):
    """عرض صفحة إرسال الدعوات للمؤسسات الحكومية"""
    # الحصول على جميع المؤسسات الحكومية
    organizations = Organization.objects.filter(organization_type='government')

    context = {
        'title': _('إرسال دعوات للمؤسسات الحكومية'),
        'organizations': organizations,
    }
    return render(request, 'organizations/government_invitations.html', context)

@staff_member_required
def admin_organizations_dashboard(request):
    """صفحة لوحة إدارة المؤسسات المخصصة للمسؤولين"""
    # إحصائيات المؤسسات
    total_organizations = Organization.objects.count()
    government_orgs = Organization.objects.filter(organization_type='government').count()
    private_orgs = Organization.objects.filter(organization_type='private').count()
    scholar_orgs = Organization.objects.filter(organization_type='scholar').count()

    # إحصائيات حالة المشاركة
    invited_count = Organization.objects.filter(participation_status='invited').count()
    confirmed_count = Organization.objects.filter(participation_status='confirmed').count()
    declined_count = Organization.objects.filter(participation_status='declined').count()
    attended_count = Organization.objects.filter(participation_status='attended').count()

    # إحصائيات الدعوات
    total_invitations = Invitation.objects.count()
    sent_invitations = Invitation.objects.filter(is_sent=True).count()
    pending_invitations = Invitation.objects.filter(is_sent=False).count()

    context = {
        'title': _('لوحة إدارة المؤسسات'),
        'total_organizations': total_organizations,
        'government_orgs': government_orgs,
        'private_orgs': private_orgs,
        'scholar_orgs': scholar_orgs,
        'invited_count': invited_count,
        'confirmed_count': confirmed_count,
        'declined_count': declined_count,
        'attended_count': attended_count,
        'total_invitations': total_invitations,
        'sent_invitations': sent_invitations,
        'pending_invitations': pending_invitations,
    }
    return render(request, 'organizations/admin_dashboard.html', context)


# ========== Scholar Views ==========

@regular_admin_required
def scholar_list(request):
    # إنشاء عالم تجريبي إذا لم يكن موجود
    scholars_with_email = Scholar.objects.filter(email__isnull=False, email__gt='')
    print(f"DEBUG: Found {scholars_with_email.count()} scholars with email")

    if not scholars_with_email.exists():
        test_scholar = Scholar.objects.create(
            name="د. محمد الأمين",
            email="<EMAIL>",
            title="doctor",
            position="أستاذ الحديث",
            organization="جامعة الأزهر",
            country="مصر",
            city="القاهرة"
        )
        print(f"DEBUG: Created test scholar: {test_scholar.name} with email: {test_scholar.email}")

    # إنشاء عالم آخر للاختبار
    if Scholar.objects.filter(email__isnull=False, email__gt='').count() < 2:
        test_scholar2 = Scholar.objects.create(
            name="د. أحمد الطيب",
            email="<EMAIL>",
            title="doctor",
            position="شيخ الأزهر",
            organization="الأزهر الشريف",
            country="مصر",
            city="القاهرة"
        )
        print(f"DEBUG: Created second test scholar: {test_scholar2.name} with email: {test_scholar2.email}")
    """عرض قائمة العلماء مع إمكانية البحث والتصفية"""
    # تطبيق فلتر البيانات حسب المسؤول
    scholars = filter_data_by_admin(Scholar.objects.all(), request.user)

    # البحث
    search_query = request.GET.get('search', '')
    if search_query:
        scholars = scholars.filter(
            Q(name__icontains=search_query) |
            Q(full_name__icontains=search_query) |
            Q(position__icontains=search_query) |
            Q(organization__icontains=search_query) |
            Q(specialization__icontains=search_query)
        )

    # التصفية حسب اللقب
    title_filter = request.GET.get('title', '')
    if title_filter:
        scholars = scholars.filter(title=title_filter)

    # التصفية حسب حالة المشاركة
    status_filter = request.GET.get('status', '')
    if status_filter:
        scholars = scholars.filter(participation_status=status_filter)

    # التصفية حسب البلد
    country_filter = request.GET.get('country', '')
    if country_filter:
        scholars = scholars.filter(country__icontains=country_filter)



    # الترتيب
    sort_by = request.GET.get('sort', 'name')
    if sort_by == 'title':
        scholars = scholars.order_by('title', 'name')
    elif sort_by == 'status':
        scholars = scholars.order_by('participation_status')
    elif sort_by == 'date':
        scholars = scholars.order_by('-created_at')
    else:  # Default to name
        scholars = scholars.order_by('name')

    # إحصائيات
    total_scholars = scholars.count()
    confirmed_scholars = scholars.filter(participation_status='confirmed').count()
    attended_scholars = scholars.filter(participation_status='attended').count()

    context = {
        'title': 'قائمة العلماء',
        'scholars': scholars,
        'search_query': search_query,
        'title_filter': title_filter,
        'status_filter': status_filter,
        'country_filter': country_filter,

        'current_sort': sort_by,
        'total_scholars': total_scholars,
        'confirmed_scholars': confirmed_scholars,
        'attended_scholars': attended_scholars,
        'title_choices': Scholar.TITLE_CHOICES,
        'status_choices': Scholar.PARTICIPATION_STATUS_CHOICES,

    }

    # إضافة معلومات المسؤول
    context.update(get_admin_context(request.user))

    return render(request, 'organizations/scholar_list.html', context)


@login_required
def scholar_detail(request, pk):
    """عرض تفاصيل عالم"""
    scholar = get_object_or_404(Scholar, pk=pk)

    context = {
        'title': scholar.get_full_title_name(),
        'scholar': scholar,
    }
    return render(request, 'organizations/scholar_detail.html', context)


@login_required
def scholar_create(request):
    """إنشاء عالم جديد"""
    if request.method == 'POST':
        # معالجة البيانات المرسلة
        title = request.POST.get('title')
        name = request.POST.get('name')
        full_name = request.POST.get('full_name')
        position = request.POST.get('position')
        organization = request.POST.get('organization')
        specialization = request.POST.get('specialization')
        country = request.POST.get('country')
        city = request.POST.get('city')
        email = request.POST.get('email')
        phone = request.POST.get('phone')
        website = request.POST.get('website')
        biography = request.POST.get('biography')
        participation_status = request.POST.get('participation_status')

        photo = request.FILES.get('photo')

        # إنشاء العالم
        scholar = Scholar.objects.create(
            title=title,
            name=name,
            full_name=full_name,
            position=position,
            organization=organization,
            specialization=specialization,
            country=country,
            city=city,
            email=email,
            phone=phone,
            website=website,
            biography=biography,
            participation_status=participation_status,
            photo=photo,
            created_by=request.user
        )

        messages.success(request, f'تم إنشاء العالم {scholar.get_full_title_name()} بنجاح')
        return redirect('organizations:scholar_detail', pk=scholar.pk)

    context = {
        'title': 'إضافة عالم جديد',
        'title_choices': Scholar.TITLE_CHOICES,
        'status_choices': Scholar.PARTICIPATION_STATUS_CHOICES,

    }
    return render(request, 'organizations/scholar_form.html', context)


@login_required
def scholar_update(request, pk):
    """تحديث بيانات عالم"""
    scholar = get_object_or_404(Scholar, pk=pk)

    if request.method == 'POST':
        # معالجة البيانات المرسلة
        scholar.title = request.POST.get('title')
        scholar.name = request.POST.get('name')
        scholar.full_name = request.POST.get('full_name')
        scholar.position = request.POST.get('position')
        scholar.organization = request.POST.get('organization')
        scholar.specialization = request.POST.get('specialization')
        scholar.country = request.POST.get('country')
        scholar.city = request.POST.get('city')
        scholar.email = request.POST.get('email')
        scholar.phone = request.POST.get('phone')
        scholar.website = request.POST.get('website')
        scholar.biography = request.POST.get('biography')
        scholar.participation_status = request.POST.get('participation_status')
        scholar.presentation_type = request.POST.get('presentation_type')
        scholar.presentation_title = request.POST.get('presentation_title')
        scholar.presentation_abstract = request.POST.get('presentation_abstract')

        # معالجة الصورة
        if 'photo' in request.FILES:
            scholar.photo = request.FILES['photo']

        scholar.save()

        messages.success(request, f'تم تحديث بيانات العالم {scholar.get_full_title_name()} بنجاح')
        return redirect('organizations:scholar_detail', pk=scholar.pk)

    context = {
        'title': f'تحديث بيانات {scholar.get_full_title_name()}',
        'scholar': scholar,
        'title_choices': Scholar.TITLE_CHOICES,
        'status_choices': Scholar.PARTICIPATION_STATUS_CHOICES,
        'presentation_choices': Scholar.PRESENTATION_CHOICES,
    }
    return render(request, 'organizations/scholar_form.html', context)


@login_required
def scholar_delete(request, pk):
    """حذف عالم"""
    scholar = get_object_or_404(Scholar, pk=pk)

    if request.method == 'POST':
        scholar_name = scholar.get_full_title_name()
        scholar.delete()
        messages.success(request, f'تم حذف العالم {scholar_name} بنجاح')
        return redirect('organizations:scholar_list')

    context = {
        'title': f'حذف {scholar.get_full_title_name()}',
        'scholar': scholar,
    }
    return render(request, 'organizations/scholar_confirm_delete.html', context)


@login_required
def scholar_export_excel(request):
    """تصدير العلماء إلى ملف Excel"""
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="scholars_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

    import openpyxl
    from openpyxl.styles import Font, Alignment

    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = 'العلماء'

    # إعداد العناوين
    headers = [
        'اللقب', 'الاسم الكامل', 'الوظيفة', 'المؤسسة', 'التخصص',
        'البلد', 'المدينة', 'نوع التقديم', 'عنوان التقديم', 'البريد الإلكتروني', 'الهاتف', 'الموقع الإلكتروني',
        'حالة المشاركة', 'تاريخ الإنشاء'
    ]

    # كتابة العناوين
    for col_num, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col_num)
        cell.value = header
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')

    # كتابة البيانات
    scholars = Scholar.objects.all().order_by('name')
    for row_num, scholar in enumerate(scholars, 2):
        worksheet.cell(row=row_num, column=1, value=scholar.get_title_display())
        worksheet.cell(row=row_num, column=2, value=scholar.get_full_title_name())
        worksheet.cell(row=row_num, column=3, value=scholar.position or '')
        worksheet.cell(row=row_num, column=4, value=scholar.organization or '')
        worksheet.cell(row=row_num, column=5, value=scholar.specialization or '')
        worksheet.cell(row=row_num, column=6, value=scholar.country or '')
        worksheet.cell(row=row_num, column=7, value=scholar.city or '')
        worksheet.cell(row=row_num, column=8, value=scholar.get_presentation_display_short())
        worksheet.cell(row=row_num, column=9, value=scholar.presentation_title or '')
        worksheet.cell(row=row_num, column=10, value=scholar.email or '')
        worksheet.cell(row=row_num, column=11, value=scholar.phone or '')
        worksheet.cell(row=row_num, column=12, value=scholar.website or '')
        worksheet.cell(row=row_num, column=13, value=scholar.get_participation_status_display())
        worksheet.cell(row=row_num, column=14, value=scholar.created_at.strftime('%Y-%m-%d'))

    workbook.save(response)
    return response


@login_required
def scholar_export_csv(request):
    """تصدير العلماء إلى ملف CSV"""
    import csv

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="scholars_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'
    response.write('\ufeff')  # BOM for UTF-8

    writer = csv.writer(response)
    writer.writerow([
        'اللقب', 'الاسم الكامل', 'الوظيفة', 'المؤسسة', 'التخصص',
        'البلد', 'المدينة', 'نوع التقديم', 'عنوان التقديم', 'البريد الإلكتروني', 'الهاتف', 'الموقع الإلكتروني',
        'حالة المشاركة', 'تاريخ الإنشاء'
    ])

    scholars = Scholar.objects.all().order_by('name')
    for scholar in scholars:
        writer.writerow([
            scholar.get_title_display(),
            scholar.get_full_title_name(),
            scholar.position or '',
            scholar.organization or '',
            scholar.specialization or '',
            scholar.country or '',
            scholar.city or '',
            scholar.get_presentation_display_short(),
            scholar.presentation_title or '',
            scholar.email or '',
            scholar.phone or '',
            scholar.website or '',
            scholar.get_participation_status_display(),
            scholar.created_at.strftime('%Y-%m-%d')
        ])

    return response


@staff_member_required
def scholar_bulk_update_status(request):
    """تحديث حالة مشاركة عدة علماء"""
    if request.method == 'POST':
        scholar_ids = request.POST.getlist('scholar_ids')
        new_status = request.POST.get('new_status')

        if scholar_ids and new_status:
            updated_count = Scholar.objects.filter(id__in=scholar_ids).update(
                participation_status=new_status
            )

            status_display = dict(Scholar.PARTICIPATION_STATUS_CHOICES).get(new_status, new_status)
            messages.success(
                request,
                f'تم تحديث حالة {updated_count} عالم إلى "{status_display}"'
            )
        else:
            messages.error(request, 'يرجى تحديد العلماء والحالة الجديدة')

    return redirect('organizations:scholar_list')


@staff_member_required
def send_scholar_invitation(request):
    """إرسال دعوة فردية لعالم"""
    if request.method == 'POST':
        form = ScholarInvitationForm(request.POST)
        if form.is_valid():
            scholar = form.cleaned_data['scholar']
            subject = form.cleaned_data['subject']
            message = form.cleaned_data['message']

            try:
                # استخدام الدالة المساعدة لإرسال الدعوة عبر WhatsApp
                invitation = send_scholar_invitation_whatsapp(request, scholar, subject, message)

                # إعادة توجيه إلى صفحة تأكيد الإرسال مع رابط WhatsApp
                messages.success(request, f'تم إعداد دعوة WhatsApp لـ {scholar.get_full_title_name()}. انقر على الرابط لإرسالها.')
                return redirect('organizations:scholar_invitation_confirm', pk=invitation.pk)
            except Exception as e:
                messages.error(request, f'فشل إعداد الدعوة: {str(e)}')
    else:
        # التحقق من وجود معرف عالم في الطلب
        scholar_id = request.GET.get('scholar_id')
        initial_data = {}

        if scholar_id and scholar_id.isdigit():
            try:
                scholar = Scholar.objects.get(id=int(scholar_id))
                initial_data['scholar'] = scholar
                # إضافة موضوع افتراضي
                initial_data['subject'] = f'دعوة للمشاركة في مؤتمر السيرة النبوية السنوي'
                # إضافة رسالة افتراضية
                initial_data['message'] = f'''بسم الله الرحمن الرحيم

فضيلة {scholar.get_full_title_name()} المحترم

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بخبرتكم وعلمكم.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر'''
            except Scholar.DoesNotExist:
                pass

        form = ScholarInvitationForm(initial=initial_data)

    context = {
        'form': form,
        'title': 'إرسال دعوة لعالم',
    }
    return render(request, 'organizations/scholar_invitation_form.html', context)


@staff_member_required
def send_bulk_scholar_invitation(request):
    """إرسال دعوات جماعية للعلماء"""
    if request.method == 'POST':
        form = BulkScholarInvitationForm(request.POST)
        if form.is_valid():
            scholars = form.cleaned_data['scholars']
            subject = form.cleaned_data['subject']
            message = form.cleaned_data['message']

            # إعداد دعوات WhatsApp
            success_count = 0
            failed_scholars = []
            invitation_links = []

            for scholar in scholars:
                try:
                    invitation = send_scholar_invitation_whatsapp(request, scholar, subject, message)
                    invitation_links.append({
                        'scholar': scholar,
                        'invitation': invitation,
                        'whatsapp_url': invitation.whatsapp_url
                    })
                    success_count += 1
                except Exception as e:
                    failed_scholars.append(scholar.get_full_title_name())
                    messages.error(request, f'فشل إعداد دعوة {scholar.get_full_title_name()}: {str(e)}')

            if success_count > 0:
                messages.success(
                    request,
                    f'تم إعداد {success_count} دعوة WhatsApp من أصل {len(scholars)} عالم. سيتم عرض روابط الإرسال.'
                )

                # حفظ روابط الدعوات في الجلسة لعرضها
                request.session['bulk_invitation_links'] = invitation_links
                return redirect('organizations:bulk_scholar_invitation_confirm')

            if failed_scholars:
                messages.warning(
                    request,
                    f'فشل إعداد الدعوات لـ: {", ".join(failed_scholars)}'
                )

            if success_count == 0:
                return redirect('organizations:scholar_list')
    else:
        # التحقق من وجود علماء محددين في الطلب
        selected_ids = request.GET.get('selected', '').split(',')
        initial_data = {
            'subject': 'دعوة للمشاركة في مؤتمر السيرة النبوية السنوي',
            'message': '''بسم الله الرحمن الرحيم

فضائل العلماء المحترمين

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بخبرتكم وعلمكم.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر'''
        }

        if selected_ids and selected_ids != ['']:
            try:
                selected_scholars = Scholar.objects.filter(id__in=selected_ids)
                initial_data['scholars'] = selected_scholars
            except (ValueError, Scholar.DoesNotExist):
                pass

        form = BulkScholarInvitationForm(initial=initial_data)

    context = {
        'form': form,
        'title': 'إرسال دعوات جماعية للعلماء',
    }
    return render(request, 'organizations/bulk_scholar_invitation_form.html', context)


@staff_member_required
def scholar_invitation_confirm(request, pk):
    """صفحة تأكيد إرسال دعوة فردية عبر WhatsApp"""
    invitation = get_object_or_404(ScholarInvitation, pk=pk)

    context = {
        'title': f'تأكيد إرسال دعوة إلى {invitation.scholar.get_full_title_name()}',
        'invitation': invitation,
        'whatsapp_url': invitation.get_whatsapp_url(),
    }
    return render(request, 'organizations/scholar_invitation_confirm.html', context)


@staff_member_required
def bulk_scholar_invitation_confirm(request):
    """صفحة تأكيد إرسال دعوات جماعية عبر WhatsApp"""
    invitation_links = request.session.get('bulk_invitation_links', [])

    if not invitation_links:
        messages.error(request, 'لا توجد دعوات لعرضها')
        return redirect('organizations:scholar_list')

    # تنظيف الجلسة بعد العرض
    del request.session['bulk_invitation_links']

    context = {
        'title': 'تأكيد إرسال الدعوات الجماعية',
        'invitation_links': invitation_links,
    }
    return render(request, 'organizations/bulk_scholar_invitation_confirm.html', context)


# ==================== Scholar Email Invitation Views ====================

@staff_member_required
def send_scholar_email_invitation(request):
    """إرسال دعوة بريد إلكتروني فردية لعالم"""
    if request.method == 'POST':
        scholar_id = request.POST.get('scholar_id')
        subject = request.POST.get('subject')
        message = request.POST.get('message')

        try:
            scholar = Scholar.objects.get(id=scholar_id)

            if not scholar.email:
                messages.error(request, f'العالم {scholar.name} لا يملك بريد إلكتروني')
                return redirect('organizations:scholar_list')

            # إرسال البريد الإلكتروني
            send_scholar_invitation_email(request, scholar, subject, message)

            messages.success(request, f'تم إرسال دعوة بريد إلكتروني بنجاح إلى {scholar.name}')

        except Scholar.DoesNotExist:
            messages.error(request, 'العالم المحدد غير موجود')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إرسال الدعوة: {str(e)}')

    return redirect('organizations:scholar_list')


@staff_member_required
def send_bulk_scholar_email_invitation(request):
    """إرسال دعوات بريد إلكتروني جماعية للعلماء"""
    if request.method == 'POST':
        scholar_ids = request.POST.get('scholar_ids', '').strip()
        subject = request.POST.get('subject', '').strip()
        message = request.POST.get('message', '').strip()

        print(f"DEBUG: scholar_ids = '{scholar_ids}'")
        print(f"DEBUG: subject = '{subject}'")
        print(f"DEBUG: message length = {len(message)}")

        if not subject or not message:
            messages.error(request, 'يرجى ملء موضوع الرسالة ونص الرسالة')
            return redirect('organizations:scholar_list')

        if not scholar_ids:
            # إرسال لجميع العلماء
            scholars = Scholar.objects.filter(email__isnull=False, email__gt='')
            print(f"DEBUG: Sending to all scholars, found {scholars.count()}")
        else:
            # إرسال للعلماء المحددين
            try:
                scholar_ids_list = [int(id.strip()) for id in scholar_ids.split(',') if id.strip()]
                scholars = Scholar.objects.filter(id__in=scholar_ids_list, email__isnull=False, email__gt='')
                print(f"DEBUG: Sending to selected scholars: {scholar_ids_list}, found {scholars.count()}")
            except ValueError as e:
                messages.error(request, f'خطأ في معرفات العلماء: {str(e)}')
                return redirect('organizations:scholar_list')

        if not scholars.exists():
            messages.error(request, 'لا يوجد علماء لديهم بريد إلكتروني لإرسال الدعوات إليهم')
            return redirect('organizations:scholar_list')

        sent_count = 0
        failed_count = 0
        failed_scholars = []

        for scholar in scholars:
            try:
                print(f"DEBUG: Sending email to {scholar.name} ({scholar.email})")
                send_scholar_invitation_email(request, scholar, subject, message)
                sent_count += 1
            except Exception as e:
                failed_count += 1
                failed_scholars.append(f"{scholar.name}: {str(e)}")
                print(f"ERROR: Failed to send to {scholar.name}: {str(e)}")

        if sent_count > 0:
            messages.success(request, f'تم إرسال {sent_count} دعوة بريد إلكتروني بنجاح')

        if failed_count > 0:
            messages.warning(request, f'فشل في إرسال {failed_count} دعوة')
            for failure in failed_scholars:
                print(f"FAILURE: {failure}")

    return redirect('organizations:scholar_list')


# ==================== Official Invitation Views ====================

@staff_member_required
def send_official_whatsapp_invitation(request):
    """إرسال دعوة WhatsApp فردية لمنتخب"""
    if request.method == 'POST':
        official_id = request.POST.get('official_id')
        subject = request.POST.get('subject')
        message = request.POST.get('message')

        try:
            official = ElectedOfficial.objects.get(id=official_id)

            if not official.phone:
                messages.error(request, f'المنتخب {official.name} لا يملك رقم هاتف')
                return redirect(request.META.get('HTTP_REFERER', 'organizations:elected_officials_list'))

            # إنشاء رابط WhatsApp
            whatsapp_url = create_whatsapp_link(official.phone, f"{subject}\n\n{message}")

            messages.success(request, f'تم إنشاء رابط WhatsApp للمنتخب {official.name}')

            # إعادة التوجيه لفتح WhatsApp
            return redirect(whatsapp_url)

        except ElectedOfficial.DoesNotExist:
            messages.error(request, 'المنتخب المحدد غير موجود')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء الدعوة: {str(e)}')

    return redirect(request.META.get('HTTP_REFERER', 'organizations:elected_officials_list'))


@staff_member_required
def send_bulk_official_whatsapp_invitation(request):
    """إرسال دعوات WhatsApp جماعية للمنتخبين"""
    if request.method == 'POST':
        official_ids = request.POST.get('official_ids', '').strip()
        subject = request.POST.get('subject', '').strip()
        message = request.POST.get('message', '').strip()

        if not subject or not message:
            messages.error(request, 'يرجى ملء موضوع الرسالة ونص الرسالة')
            return redirect(request.META.get('HTTP_REFERER', 'organizations:elected_officials_list'))

        if not official_ids:
            # إرسال لجميع المنتخبين
            officials = ElectedOfficial.objects.filter(phone__isnull=False, phone__gt='')
        else:
            # إرسال للمنتخبين المحددين
            try:
                official_ids_list = [int(id.strip()) for id in official_ids.split(',') if id.strip()]
                officials = ElectedOfficial.objects.filter(id__in=official_ids_list, phone__isnull=False, phone__gt='')
            except ValueError as e:
                messages.error(request, f'خطأ في معرفات المنتخبين: {str(e)}')
                return redirect(request.META.get('HTTP_REFERER', 'organizations:elected_officials_list'))

        if not officials.exists():
            messages.error(request, 'لا يوجد منتخبين لديهم رقم هاتف لإرسال الدعوات إليهم')
            return redirect(request.META.get('HTTP_REFERER', 'organizations:elected_officials_list'))

        # إنشاء روابط WhatsApp
        invitation_links = []
        for official in officials:
            try:
                whatsapp_url = create_whatsapp_link(official.phone, f"{subject}\n\n{message}")
                invitation_links.append({
                    'name': official.name,
                    'phone': official.phone,
                    'url': whatsapp_url
                })
            except Exception as e:
                print(f"خطأ في إنشاء رابط WhatsApp للمنتخب {official.name}: {str(e)}")

        if invitation_links:
            # حفظ الروابط في الجلسة لعرضها
            request.session['bulk_invitation_links'] = invitation_links
            messages.success(request, f'تم إنشاء {len(invitation_links)} رابط WhatsApp')
            return redirect('organizations:bulk_official_invitation_confirm')
        else:
            messages.error(request, 'فشل في إنشاء روابط WhatsApp')

    return redirect(request.META.get('HTTP_REFERER', 'organizations:elected_officials_list'))


@staff_member_required
def send_official_email_invitation(request):
    """إرسال دعوة بريد إلكتروني فردية لمنتخب"""
    if request.method == 'POST':
        official_id = request.POST.get('official_id')
        subject = request.POST.get('subject')
        message = request.POST.get('message')

        try:
            official = ElectedOfficial.objects.get(id=official_id)

            if not official.email:
                messages.error(request, f'المنتخب {official.name} لا يملك بريد إلكتروني')
                return redirect(request.META.get('HTTP_REFERER', 'organizations:elected_officials_list'))

            # إرسال البريد الإلكتروني
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[official.email],
                fail_silently=False,
            )

            messages.success(request, f'تم إرسال دعوة بريد إلكتروني بنجاح إلى {official.name}')

        except ElectedOfficial.DoesNotExist:
            messages.error(request, 'المنتخب المحدد غير موجود')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إرسال الدعوة: {str(e)}')

    return redirect(request.META.get('HTTP_REFERER', 'organizations:elected_officials_list'))


@staff_member_required
def send_bulk_official_email_invitation(request):
    """إرسال دعوات بريد إلكتروني جماعية للمنتخبين"""
    if request.method == 'POST':
        official_ids = request.POST.get('official_ids', '').strip()
        subject = request.POST.get('subject', '').strip()
        message = request.POST.get('message', '').strip()

        if not subject or not message:
            messages.error(request, 'يرجى ملء موضوع الرسالة ونص الرسالة')
            return redirect(request.META.get('HTTP_REFERER', 'organizations:elected_officials_list'))

        if not official_ids:
            # إرسال لجميع المنتخبين
            officials = ElectedOfficial.objects.filter(email__isnull=False, email__gt='')
        else:
            # إرسال للمنتخبين المحددين
            try:
                official_ids_list = [int(id.strip()) for id in official_ids.split(',') if id.strip()]
                officials = ElectedOfficial.objects.filter(id__in=official_ids_list, email__isnull=False, email__gt='')
            except ValueError as e:
                messages.error(request, f'خطأ في معرفات المنتخبين: {str(e)}')
                return redirect(request.META.get('HTTP_REFERER', 'organizations:elected_officials_list'))

        if not officials.exists():
            messages.error(request, 'لا يوجد منتخبين لديهم بريد إلكتروني لإرسال الدعوات إليهم')
            return redirect(request.META.get('HTTP_REFERER', 'organizations:elected_officials_list'))

        sent_count = 0
        failed_count = 0

        for official in officials:
            try:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[official.email],
                    fail_silently=False,
                )
                sent_count += 1
            except Exception as e:
                failed_count += 1
                print(f"خطأ في إرسال دعوة إلى {official.name}: {str(e)}")

        if sent_count > 0:
            messages.success(request, f'تم إرسال {sent_count} دعوة بريد إلكتروني بنجاح')

        if failed_count > 0:
            messages.warning(request, f'فشل في إرسال {failed_count} دعوة')

    return redirect(request.META.get('HTTP_REFERER', 'organizations:elected_officials_list'))


@staff_member_required
def bulk_official_invitation_confirm(request):
    """صفحة تأكيد إرسال دعوات جماعية عبر WhatsApp للمنتخبين"""
    invitation_links = request.session.get('bulk_invitation_links', [])

    if not invitation_links:
        messages.error(request, 'لا توجد دعوات لعرضها')
        return redirect('organizations:elected_officials_list')

    # تنظيف الجلسة بعد العرض
    del request.session['bulk_invitation_links']

    context = {
        'title': 'تأكيد إرسال الدعوات الجماعية للمنتخبين',
        'invitation_links': invitation_links,
    }
    return render(request, 'organizations/bulk_official_invitation_confirm.html', context)


# ==================== Former Minister Invitation Views ====================

@staff_member_required
def send_minister_whatsapp_invitation(request):
    """إرسال دعوة WhatsApp فردية لوزير سابق"""
    if request.method == 'POST':
        minister_id = request.POST.get('minister_id')
        subject = request.POST.get('subject')
        message = request.POST.get('message')

        try:
            minister = FormerMinister.objects.get(id=minister_id)

            if not minister.phone:
                messages.error(request, f'الوزير السابق {minister.name} لا يملك رقم هاتف')
                return redirect('organizations:former_ministers_list')

            # إنشاء رابط WhatsApp
            whatsapp_url = create_whatsapp_link(minister.phone, f"{subject}\n\n{message}")

            messages.success(request, f'تم إنشاء رابط WhatsApp للوزير السابق {minister.name}')

            # إعادة التوجيه لفتح WhatsApp
            return redirect(whatsapp_url)

        except FormerMinister.DoesNotExist:
            messages.error(request, 'الوزير السابق المحدد غير موجود')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء الدعوة: {str(e)}')

    return redirect('organizations:former_ministers_list')


@staff_member_required
def send_bulk_minister_whatsapp_invitation(request):
    """إرسال دعوات WhatsApp جماعية للوزراء السابقين"""
    if request.method == 'POST':
        minister_ids = request.POST.get('minister_ids', '').strip()
        subject = request.POST.get('subject', '').strip()
        message = request.POST.get('message', '').strip()

        if not subject or not message:
            messages.error(request, 'يرجى ملء موضوع الرسالة ونص الرسالة')
            return redirect('organizations:former_ministers_list')

        if not minister_ids:
            # إرسال لجميع الوزراء السابقين
            ministers = FormerMinister.objects.filter(phone__isnull=False, phone__gt='')
        else:
            # إرسال للوزراء المحددين
            try:
                minister_ids_list = [int(id.strip()) for id in minister_ids.split(',') if id.strip()]
                ministers = FormerMinister.objects.filter(id__in=minister_ids_list, phone__isnull=False, phone__gt='')
            except ValueError as e:
                messages.error(request, f'خطأ في معرفات الوزراء: {str(e)}')
                return redirect('organizations:former_ministers_list')

        if not ministers.exists():
            messages.error(request, 'لا يوجد وزراء سابقين لديهم رقم هاتف لإرسال الدعوات إليهم')
            return redirect('organizations:former_ministers_list')

        # إنشاء روابط WhatsApp
        invitation_links = []
        for minister in ministers:
            try:
                whatsapp_url = create_whatsapp_link(minister.phone, f"{subject}\n\n{message}")
                invitation_links.append({
                    'name': minister.name,
                    'phone': minister.phone,
                    'url': whatsapp_url
                })
            except Exception as e:
                print(f"خطأ في إنشاء رابط WhatsApp للوزير السابق {minister.name}: {str(e)}")

        if invitation_links:
            # حفظ الروابط في الجلسة لعرضها
            request.session['bulk_invitation_links'] = invitation_links
            messages.success(request, f'تم إنشاء {len(invitation_links)} رابط WhatsApp')
            return redirect('organizations:bulk_minister_invitation_confirm')
        else:
            messages.error(request, 'فشل في إنشاء روابط WhatsApp')

    return redirect('organizations:former_ministers_list')


@staff_member_required
def send_minister_email_invitation(request):
    """إرسال دعوة بريد إلكتروني فردية لوزير سابق"""
    if request.method == 'POST':
        minister_id = request.POST.get('minister_id')
        subject = request.POST.get('subject')
        message = request.POST.get('message')

        try:
            minister = FormerMinister.objects.get(id=minister_id)

            if not minister.email:
                messages.error(request, f'الوزير السابق {minister.name} لا يملك بريد إلكتروني')
                return redirect('organizations:former_ministers_list')

            # إرسال البريد الإلكتروني
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[minister.email],
                fail_silently=False,
            )

            messages.success(request, f'تم إرسال دعوة بريد إلكتروني بنجاح إلى الوزير السابق {minister.name}')

        except FormerMinister.DoesNotExist:
            messages.error(request, 'الوزير السابق المحدد غير موجود')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إرسال الدعوة: {str(e)}')

    return redirect('organizations:former_ministers_list')


@staff_member_required
def send_bulk_minister_email_invitation(request):
    """إرسال دعوات بريد إلكتروني جماعية للوزراء السابقين"""
    if request.method == 'POST':
        minister_ids = request.POST.get('minister_ids', '').strip()
        subject = request.POST.get('subject', '').strip()
        message = request.POST.get('message', '').strip()

        if not subject or not message:
            messages.error(request, 'يرجى ملء موضوع الرسالة ونص الرسالة')
            return redirect('organizations:former_ministers_list')

        if not minister_ids:
            # إرسال لجميع الوزراء السابقين
            ministers = FormerMinister.objects.filter(email__isnull=False, email__gt='')
        else:
            # إرسال للوزراء المحددين
            try:
                minister_ids_list = [int(id.strip()) for id in minister_ids.split(',') if id.strip()]
                ministers = FormerMinister.objects.filter(id__in=minister_ids_list, email__isnull=False, email__gt='')
            except ValueError as e:
                messages.error(request, f'خطأ في معرفات الوزراء: {str(e)}')
                return redirect('organizations:former_ministers_list')

        if not ministers.exists():
            messages.error(request, 'لا يوجد وزراء سابقين لديهم بريد إلكتروني لإرسال الدعوات إليهم')
            return redirect('organizations:former_ministers_list')

        sent_count = 0
        failed_count = 0

        for minister in ministers:
            try:
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[minister.email],
                    fail_silently=False,
                )
                sent_count += 1
            except Exception as e:
                failed_count += 1
                print(f"خطأ في إرسال دعوة إلى الوزير السابق {minister.name}: {str(e)}")

        if sent_count > 0:
            messages.success(request, f'تم إرسال {sent_count} دعوة بريد إلكتروني بنجاح')

        if failed_count > 0:
            messages.warning(request, f'فشل في إرسال {failed_count} دعوة')

    return redirect('organizations:former_ministers_list')


@staff_member_required
def bulk_minister_invitation_confirm(request):
    """صفحة تأكيد إرسال دعوات جماعية عبر WhatsApp للوزراء السابقين"""
    invitation_links = request.session.get('bulk_invitation_links', [])

    if not invitation_links:
        messages.error(request, 'لا توجد دعوات لعرضها')
        return redirect('organizations:former_ministers_list')

    # تنظيف الجلسة بعد العرض
    del request.session['bulk_invitation_links']

    context = {
        'title': 'تأكيد إرسال الدعوات الجماعية للوزراء السابقين',
        'invitation_links': invitation_links,
    }
    return render(request, 'organizations/bulk_minister_invitation_confirm.html', context)


# ==================== Diplomatic Corps Invitation Views ====================

@staff_member_required
def send_diplomat_whatsapp_invitation(request):
    """إرسال دعوة WhatsApp فردية لدبلوماسي"""
    if request.method == 'POST':
        diplomat_id = request.POST.get('diplomat_id')
        subject = request.POST.get('subject')
        message = request.POST.get('message')

        try:
            diplomat = DiplomaticCorps.objects.get(id=diplomat_id)

            if not diplomat.phone:
                messages.error(request, f'الدبلوماسي {diplomat.name} لا يملك رقم هاتف')
                return redirect('organizations:diplomatic_corps_list')

            # إنشاء رابط WhatsApp
            whatsapp_url = create_whatsapp_link(diplomat.phone, f"{subject}\n\n{message}")

            messages.success(request, f'تم إنشاء رابط WhatsApp للدبلوماسي {diplomat.name}')

            # إعادة التوجيه لفتح WhatsApp
            return redirect(whatsapp_url)

        except DiplomaticCorps.DoesNotExist:
            messages.error(request, 'الدبلوماسي المحدد غير موجود')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء الدعوة: {str(e)}')

    return redirect('organizations:diplomatic_corps_list')


@staff_member_required
def send_diplomat_email_invitation(request):
    """إرسال دعوة بريد إلكتروني فردية لدبلوماسي"""
    if request.method == 'POST':
        diplomat_id = request.POST.get('diplomat_id')
        subject = request.POST.get('subject')
        message = request.POST.get('message')

        try:
            diplomat = DiplomaticCorps.objects.get(id=diplomat_id)

            if not diplomat.email:
                messages.error(request, f'الدبلوماسي {diplomat.name} لا يملك بريد إلكتروني')
                return redirect('organizations:diplomatic_corps_list')

            # إرسال البريد الإلكتروني
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[diplomat.email],
                fail_silently=False,
            )

            messages.success(request, f'تم إرسال دعوة بريد إلكتروني بنجاح إلى الدبلوماسي {diplomat.name}')

        except DiplomaticCorps.DoesNotExist:
            messages.error(request, 'الدبلوماسي المحدد غير موجود')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إرسال الدعوة: {str(e)}')

    return redirect('organizations:diplomatic_corps_list')


# ==================== Notable Invitation Views ====================

@staff_member_required
def send_notable_whatsapp_invitation(request):
    """إرسال دعوة WhatsApp فردية لعين"""
    if request.method == 'POST':
        notable_id = request.POST.get('notable_id')
        subject = request.POST.get('subject')
        message = request.POST.get('message')

        try:
            notable = Notable.objects.get(id=notable_id)

            if not notable.phone:
                messages.error(request, f'العين {notable.name} لا يملك رقم هاتف')
                return redirect('organizations:notables_list')

            # إنشاء رابط WhatsApp
            whatsapp_url = create_whatsapp_link(notable.phone, f"{subject}\n\n{message}")

            messages.success(request, f'تم إنشاء رابط WhatsApp للعين {notable.name}')

            # إعادة التوجيه لفتح WhatsApp
            return redirect(whatsapp_url)

        except Notable.DoesNotExist:
            messages.error(request, 'العين المحدد غير موجود')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء الدعوة: {str(e)}')

    return redirect('organizations:notables_list')


@staff_member_required
def send_notable_email_invitation(request):
    """إرسال دعوة بريد إلكتروني فردية لعين"""
    if request.method == 'POST':
        notable_id = request.POST.get('notable_id')
        subject = request.POST.get('subject')
        message = request.POST.get('message')

        try:
            notable = Notable.objects.get(id=notable_id)

            if not notable.email:
                messages.error(request, f'العين {notable.name} لا يملك بريد إلكتروني')
                return redirect('organizations:notables_list')

            # إرسال البريد الإلكتروني
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[notable.email],
                fail_silently=False,
            )

            messages.success(request, f'تم إرسال دعوة بريد إلكتروني بنجاح إلى العين {notable.name}')

        except Notable.DoesNotExist:
            messages.error(request, 'العين المحدد غير موجود')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إرسال الدعوة: {str(e)}')

    return redirect('organizations:notables_list')


# ==================== Views للمنتخبين ====================

@regular_admin_required
def elected_officials_list(request):
    """عرض قائمة جميع المنتخبين"""
    # تطبيق فلتر البيانات حسب المسؤول
    officials = filter_data_by_admin(ElectedOfficial.objects.all(), request.user).order_by('name')

    # فلترة حسب المنصب
    position_filter = request.GET.get('position')
    if position_filter:
        officials = officials.filter(position=position_filter)

    # فلترة حسب الحالة
    status_filter = request.GET.get('status')
    if status_filter:
        officials = officials.filter(status=status_filter)

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        officials = officials.filter(
            Q(name__icontains=search_query) |
            Q(region__icontains=search_query) |
            Q(party__icontains=search_query)
        )

    # إحصائيات
    stats = {
        'total': ElectedOfficial.objects.count(),
        'mayors': ElectedOfficial.objects.filter(position='mayor').count(),
        'deputies': ElectedOfficial.objects.filter(position='deputy').count(),
        'heads': ElectedOfficial.objects.filter(position='head').count(),
        'active': ElectedOfficial.objects.filter(status='active').count(),
    }

    context = {
        'officials': officials,
        'stats': stats,
        'title': 'قائمة المنتخبين',
        'position_choices': ElectedOfficial.POSITION_CHOICES,
        'status_choices': ElectedOfficial.STATUS_CHOICES,
        'current_position': position_filter,
        'current_status': status_filter,
        'search_query': search_query,
    }

    # إضافة معلومات المسؤول
    context.update(get_admin_context(request.user))

    return render(request, 'organizations/elected_officials_list.html', context)


@login_required
def party_leaders_list(request):
    """عرض قائمة رؤساء الأحزاب الموريتانية"""

    # قائمة رؤساء الأحزاب الموريتانية
    party_leaders = [
        {
            'name': 'معالي السيد قاري محمد عبد الله',
            'party': 'حزب الاتحاد من أجل التخطيط للبناء',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيد سيدي أحمد ولد محمد',
            'party': 'حزب الإنصاف',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'سيادة الرئيس الشيخ بوي ولد شيخنا تقي اللـه',
            'party': 'حزب الوحدة والتنمية',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيد حمادي سيدي المختار',
            'party': 'حزب التجمع الوطني للإصلاح والتنمية "تواصل"',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيدة الناها بنت مكناس',
            'party': 'حزب الاتحاد من أجل الديمقراطية والتقدم',
            'title': 'رئيسة الحزب'
        },
        {
            'name': 'معالي السيد آمادو تيجاني جوب',
            'party': 'حزب الجبهة الجمهورية للوحدة والديمقراطية',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيد محمد أحمد سالم طالبن',
            'party': 'حزب الإصلاح',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيد يعقوب محمد عبد الرحمن أمين',
            'party': 'حزب التحالف الوطني الديمقراطي',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيد عبد السلام حرمه',
            'party': 'حزب الصواب',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيد شيخنا حجبو',
            'party': 'حزب الكرامة',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيد داوود عبد الله احمد عيش',
            'party': 'حزب نداء الوطن',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيد إبراهيم مختار صار',
            'party': 'حزب التحالف من أجل العدالة والديمقراطية',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيد صالح محمدو حننه',
            'party': 'الحزب الموريتاني من أجل الاتحاد والتغيير (حاتم)',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيدة فاله بنت ميني',
            'party': 'حزب حوار',
            'title': 'رئيسة الحزب'
        },
        {
            'name': 'معالي السيد الشيخ عثمان أبو المعالي',
            'party': 'حزب الفضيلة',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيد عالي محمد عبد الله',
            'party': 'حزب الكتل الموريتانية',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيد أحمد ولد داداه',
            'party': 'حزب تكتل القوى الديمقراطية',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيد محمد ولد مولود',
            'party': 'حزب اتحاد قوى التقدم',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي السيد مسعود ولد بلخير',
            'party': 'حزب التحالف الشعبي التقدمي',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'صاحبة المعالي منتاته بنت حديد',
            'party': 'حزب غير محدد',
            'title': 'رئيسة الحزب'
        },
        {
            'name': 'سيادة الرئيس محمد ول فال',
            'party': 'حزب الرفاه',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي الرئيسة لالة الشريف هاشم',
            'party': 'حزب الحراك الشبابي',
            'title': 'رئيسة الحزب'
        },
        {
            'name': 'سيادة الرئيس د. السعد لوليد',
            'party': 'حزب غير محدد',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'معالي الرئيس محمد بربص',
            'party': 'حزب غير محدد',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'سيادة الرئيس سيدي محمد علي محمد العبد',
            'party': 'حزب المسار',
            'title': 'رئيس الحزب'
        },
        {
            'name': 'سيادة الرئيس محمد جميل منصور',
            'party': 'جبهة المواطنة والعدالة',
            'title': 'رئيس الجبهة'
        }
    ]

    context = {
        'title': 'رؤساء الأحزاب الموريتانية',
        'party_leaders': party_leaders,
        'total_leaders': len(party_leaders),
    }

    # إضافة معلومات المسؤول
    context.update(get_admin_context(request.user))

    return render(request, 'organizations/party_leaders_list.html', context)


@login_required
def mayors_list(request):
    """عرض قائمة العمد"""
    # إنشاء عمد تجريبيين إذا لم يكونوا موجودين
    if not ElectedOfficial.objects.filter(position='mayor').exists():
        ElectedOfficial.objects.create(
            name="العمدة محمد ولد أحمد",
            position="mayor",
            region="تفرغ زينة",
            party="مستقل",
            phone="+22222123456",
            email="<EMAIL>",
            status="active"
        )
        ElectedOfficial.objects.create(
            name="العمدة عائشة بنت محمد",
            position="mayor",
            region="كيفة",
            party="حزب الاتحاد",
            phone="+22222234567",
            email="<EMAIL>",
            status="active"
        )

    mayors = ElectedOfficial.objects.filter(position='mayor').order_by('name')

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        mayors = mayors.filter(
            Q(name__icontains=search_query) |
            Q(region__icontains=search_query)
        )

    context = {
        'officials': mayors,
        'title': 'قسم العمد',
        'position_type': 'عمدة',
        'search_query': search_query,
    }
    return render(request, 'organizations/elected_officials_by_position.html', context)


@login_required
def deputies_list(request):
    """عرض قائمة النواب"""
    # إنشاء نواب تجريبيين إذا لم يكونوا موجودين
    if not ElectedOfficial.objects.filter(position='deputy').exists():
        ElectedOfficial.objects.create(
            name="النائب أحمد ولد محمد",
            position="deputy",
            region="نواكشوط الشمالية",
            party="حزب الاتحاد",
            phone="+22222345678",
            email="<EMAIL>",
            status="active"
        )
        ElectedOfficial.objects.create(
            name="النائبة فاطمة بنت عبد الله",
            position="deputy",
            region="نواكشوط الجنوبية",
            party="حزب التجمع",
            phone="+22222456789",
            email="<EMAIL>",
            status="active"
        )

    deputies = ElectedOfficial.objects.filter(position='deputy').order_by('name')

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        deputies = deputies.filter(
            Q(name__icontains=search_query) |
            Q(region__icontains=search_query) |
            Q(party__icontains=search_query)
        )

    context = {
        'officials': deputies,
        'title': 'قسم النواب',
        'position_type': 'نائب',
        'search_query': search_query,
    }
    return render(request, 'organizations/elected_officials_by_position.html', context)


@login_required
def heads_list(request):
    """عرض قائمة رؤساء الجهات"""
    # إنشاء رؤساء جهات تجريبيين إذا لم يكونوا موجودين
    if not ElectedOfficial.objects.filter(position='head').exists():
        ElectedOfficial.objects.create(
            name="رئيس الجهة عبد الله ولد محمد",
            position="head",
            region="جهة نواكشوط",
            party="حزب التجمع",
            phone="+22222567890",
            email="<EMAIL>",
            status="active"
        )
        ElectedOfficial.objects.create(
            name="رئيس الجهة مريم بنت أحمد",
            position="head",
            region="جهة الحوض الشرقي",
            party="حزب الاتحاد",
            phone="+22222678901",
            email="<EMAIL>",
            status="active"
        )

    heads = ElectedOfficial.objects.filter(position='head').order_by('name')

    # البحث
    search_query = request.GET.get('search')
    if search_query:
        heads = heads.filter(
            Q(name__icontains=search_query) |
            Q(region__icontains=search_query)
        )

    context = {
        'officials': heads,
        'title': 'قسم رؤساء الجهات',
        'position_type': 'رئيس جهة',
        'search_query': search_query,
    }
    return render(request, 'organizations/elected_officials_by_position.html', context)


@regular_admin_required
def add_elected_official(request):
    """إضافة منتخب جديد"""
    if request.method == 'POST':
        form = ElectedOfficialForm(request.POST)
        if form.is_valid():
            official = form.save(commit=False)
            official.created_by = request.user
            official.save()
            messages.success(request, f'تم إضافة المنتخب {official.name} بنجاح')
            return redirect('organizations:elected_officials_list')
    else:
        form = ElectedOfficialForm()

    context = {
        'form': form,
        'title': 'إضافة منتخب جديد',
        'submit_text': 'إضافة',
    }
    return render(request, 'organizations/elected_official_form.html', context)


@login_required
def edit_elected_official(request, pk):
    """تعديل بيانات منتخب"""
    official = get_object_or_404(ElectedOfficial, pk=pk)

    if request.method == 'POST':
        form = ElectedOfficialForm(request.POST, instance=official)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات {official.name} بنجاح')
            return redirect('organizations:elected_officials_list')
    else:
        form = ElectedOfficialForm(instance=official)

    context = {
        'form': form,
        'official': official,
        'title': f'تعديل بيانات {official.name}',
        'submit_text': 'حفظ التغييرات',
    }
    return render(request, 'organizations/elected_official_form.html', context)


@login_required
def delete_elected_official(request, pk):
    """حذف منتخب"""
    official = get_object_or_404(ElectedOfficial, pk=pk)

    if request.method == 'POST':
        name = official.name
        official.delete()
        messages.success(request, f'تم حذف المنتخب {name} بنجاح')
        return redirect('organizations:elected_officials_list')

    context = {
        'official': official,
        'title': f'حذف المنتخب {official.name}',
    }
    return render(request, 'organizations/confirm_delete.html', context)


@login_required
def export_elected_officials(request):
    """تصدير المنتخبين إلى Excel"""
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename="elected_officials.xlsx"'

    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = 'المنتخبون'

    # إعداد العناوين
    headers = ['الاسم', 'المنصب', 'المنطقة', 'الحزب', 'الهاتف', 'البريد الإلكتروني', 'الحالة', 'تاريخ البداية', 'تاريخ النهاية']
    for col_num, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col_num)
        cell.value = header
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')

    # إضافة البيانات
    officials = ElectedOfficial.objects.all().order_by('name')
    for row_num, official in enumerate(officials, 2):
        worksheet.cell(row=row_num, column=1, value=official.name)
        worksheet.cell(row=row_num, column=2, value=official.get_position_display())
        worksheet.cell(row=row_num, column=3, value=official.region)
        worksheet.cell(row=row_num, column=4, value=official.party or '')
        worksheet.cell(row=row_num, column=5, value=official.phone or '')
        worksheet.cell(row=row_num, column=6, value=official.email or '')
        worksheet.cell(row=row_num, column=7, value=official.get_status_display())
        worksheet.cell(row=row_num, column=8, value=official.start_date.strftime('%Y-%m-%d') if official.start_date else '')
        worksheet.cell(row=row_num, column=9, value=official.end_date.strftime('%Y-%m-%d') if official.end_date else '')

    workbook.save(response)
    return response


# ==================== الوزراء السابقون ====================

@regular_admin_required
def former_ministers_list(request):
    """عرض قائمة الوزراء السابقين"""
    # إنشاء وزراء سابقين تجريبيين إذا لم يكونوا موجودين
    if not FormerMinister.objects.exists():
        FormerMinister.objects.create(
            name="د. محمد ولد عبد الله",
            ministry="التعليم العالي",
            government="حكومة 2019-2024",
            start_date="2019-01-01",
            end_date="2024-01-01",
            phone="+22222789012",
            email="<EMAIL>",
            status="active"
        )
        FormerMinister.objects.create(
            name="د. فاطمة بنت أحمد",
            ministry="الصحة",
            government="حكومة 2015-2019",
            start_date="2015-01-01",
            end_date="2019-01-01",
            phone="+22222890123",
            email="<EMAIL>",
            status="active"
        )

    # تطبيق فلتر البيانات حسب المسؤول
    ministers = filter_data_by_admin(FormerMinister.objects.all(), request.user)

    # البحث
    search_query = request.GET.get('search', '')
    if search_query:
        ministers = ministers.filter(
            Q(name__icontains=search_query) |
            Q(ministry__icontains=search_query) |
            Q(government__icontains=search_query) |
            Q(current_position__icontains=search_query)
        )

    # الفلترة حسب الوزارة
    ministry_filter = request.GET.get('ministry', '')
    if ministry_filter:
        ministers = ministers.filter(ministry__icontains=ministry_filter)

    # الفلترة حسب الحالة
    status_filter = request.GET.get('status', '')
    if status_filter:
        ministers = ministers.filter(status=status_filter)

    # الفلترة حسب الحكومة
    government_filter = request.GET.get('government', '')
    if government_filter:
        ministers = ministers.filter(government__icontains=government_filter)

    # إحصائيات
    stats = {
        'total': FormerMinister.objects.count(),
        'active': FormerMinister.objects.filter(status='active').count(),
        'inactive': FormerMinister.objects.filter(status='inactive').count(),
        'deceased': FormerMinister.objects.filter(status='deceased').count(),
    }

    # الحصول على قوائم للفلاتر
    ministries = FormerMinister.objects.values_list('ministry', flat=True).distinct().order_by('ministry')
    governments = FormerMinister.objects.values_list('government', flat=True).distinct().order_by('government')
    status_choices = FormerMinister.STATUS_CHOICES

    context = {
        'ministers': ministers,
        'stats': stats,
        'search_query': search_query,
        'ministry_filter': ministry_filter,
        'status_filter': status_filter,
        'government_filter': government_filter,
        'ministries': ministries,
        'governments': governments,
        'status_choices': status_choices,
        'title': 'الوزراء السابقون',
    }

    # إضافة معلومات المسؤول
    context.update(get_admin_context(request.user))

    return render(request, 'organizations/former_ministers_list.html', context)


@regular_admin_required
def add_former_minister(request):
    """إضافة وزير سابق جديد"""
    if request.method == 'POST':
        form = FormerMinisterForm(request.POST)
        if form.is_valid():
            minister = form.save(commit=False)
            minister.created_by = request.user
            minister.save()
            messages.success(request, f'تم إضافة الوزير السابق {minister.name} بنجاح.')
            return redirect('organizations:former_ministers_list')
    else:
        form = FormerMinisterForm()

    context = {
        'form': form,
        'title': 'إضافة وزير سابق جديد',
        'submit_text': 'إضافة الوزير',
    }

    return render(request, 'organizations/former_minister_form.html', context)


@login_required
def edit_former_minister(request, pk):
    """تعديل بيانات وزير سابق"""
    minister = get_object_or_404(FormerMinister, pk=pk)

    if request.method == 'POST':
        form = FormerMinisterForm(request.POST, instance=minister)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات الوزير السابق {minister.name} بنجاح.')
            return redirect('organizations:former_ministers_list')
    else:
        form = FormerMinisterForm(instance=minister)

    context = {
        'form': form,
        'minister': minister,
        'title': f'تعديل بيانات {minister.name}',
        'submit_text': 'حفظ التغييرات',
    }

    return render(request, 'organizations/former_minister_form.html', context)


@login_required
def delete_former_minister(request, pk):
    """حذف وزير سابق"""
    minister = get_object_or_404(FormerMinister, pk=pk)

    if request.method == 'POST':
        minister_name = minister.name
        minister.delete()
        messages.success(request, f'تم حذف الوزير السابق {minister_name} بنجاح.')
        return redirect('organizations:former_ministers_list')

    context = {
        'minister': minister,
        'title': f'حذف {minister.name}',
    }

    return render(request, 'organizations/delete_former_minister.html', context)


@login_required
def export_former_ministers(request):
    """تصدير الوزراء السابقين إلى Excel"""
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename="former_ministers.xlsx"'

    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = 'الوزراء السابقون'

    # إعداد العناوين
    headers = ['الاسم', 'الوزارة', 'الحكومة', 'تاريخ البداية', 'تاريخ النهاية', 'مدة الخدمة', 'الحالة', 'المنصب الحالي', 'الهاتف', 'البريد الإلكتروني']
    for col_num, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col_num)
        cell.value = header
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')

    # إضافة البيانات
    ministers = FormerMinister.objects.all()
    for row_num, minister in enumerate(ministers, 2):
        worksheet.cell(row=row_num, column=1, value=minister.name)
        worksheet.cell(row=row_num, column=2, value=minister.ministry)
        worksheet.cell(row=row_num, column=3, value=minister.government or '')
        worksheet.cell(row=row_num, column=4, value=minister.start_date.strftime('%Y-%m-%d') if minister.start_date else '')
        worksheet.cell(row=row_num, column=5, value=minister.end_date.strftime('%Y-%m-%d') if minister.end_date else '')
        worksheet.cell(row=row_num, column=6, value=minister.get_service_duration())
        worksheet.cell(row=row_num, column=7, value=minister.get_status_display())
        worksheet.cell(row=row_num, column=8, value=minister.current_position or '')
        worksheet.cell(row=row_num, column=9, value=minister.phone or '')
        worksheet.cell(row=row_num, column=10, value=minister.email or '')

    workbook.save(response)
    return response


# ==================== السلك الدبلوماسي ====================

@regular_admin_required
def diplomatic_corps_list(request):
    """عرض قائمة السلك الدبلوماسي"""
    # إنشاء دبلوماسيين تجريبيين إذا لم يكونوا موجودين
    if not DiplomaticCorps.objects.exists():
        DiplomaticCorps.objects.create(
            name="السفير أحمد ولد محمد",
            rank="ambassador",
            country="فرنسا",
            mission_type="embassy",
            current_position="سفير موريتانيا في فرنسا",
            phone="+33123456789",
            email="<EMAIL>",
            status="active"
        )
        DiplomaticCorps.objects.create(
            name="القنصل فاطمة بنت عبد الله",
            rank="consul",
            country="المغرب",
            mission_type="consulate",
            current_position="قنصل موريتانيا في الرباط",
            phone="+212987654321",
            email="<EMAIL>",
            status="active"
        )

    # تطبيق فلتر البيانات حسب المسؤول
    diplomats = filter_data_by_admin(DiplomaticCorps.objects.all(), request.user)

    # البحث
    search_query = request.GET.get('search', '')
    if search_query:
        diplomats = diplomats.filter(
            Q(name__icontains=search_query) |
            Q(country__icontains=search_query) |
            Q(current_position__icontains=search_query) |
            Q(languages__icontains=search_query)
        )

    # الفلترة حسب الرتبة
    rank_filter = request.GET.get('rank', '')
    if rank_filter:
        diplomats = diplomats.filter(rank=rank_filter)

    # الفلترة حسب نوع المهمة
    mission_type_filter = request.GET.get('mission_type', '')
    if mission_type_filter:
        diplomats = diplomats.filter(mission_type=mission_type_filter)

    # الفلترة حسب الحالة
    status_filter = request.GET.get('status', '')
    if status_filter:
        diplomats = diplomats.filter(status=status_filter)

    # الفلترة حسب البلد
    country_filter = request.GET.get('country', '')
    if country_filter:
        diplomats = diplomats.filter(country__icontains=country_filter)

    # إحصائيات
    stats = {
        'total': DiplomaticCorps.objects.count(),
        'active': DiplomaticCorps.objects.filter(status='active').count(),
        'inactive': DiplomaticCorps.objects.filter(status='inactive').count(),
        'retired': DiplomaticCorps.objects.filter(status='retired').count(),
        'ambassadors': DiplomaticCorps.objects.filter(rank='ambassador').count(),
    }

    # الحصول على قوائم للفلاتر
    countries = DiplomaticCorps.objects.values_list('country', flat=True).distinct().order_by('country')
    rank_choices = DiplomaticCorps.RANK_CHOICES
    mission_type_choices = DiplomaticCorps.MISSION_TYPE_CHOICES
    status_choices = DiplomaticCorps.STATUS_CHOICES

    context = {
        'diplomats': diplomats,
        'stats': stats,
        'search_query': search_query,
        'rank_filter': rank_filter,
        'mission_type_filter': mission_type_filter,
        'status_filter': status_filter,
        'country_filter': country_filter,
        'countries': countries,
        'rank_choices': rank_choices,
        'mission_type_choices': mission_type_choices,
        'status_choices': status_choices,
        'title': 'السلك الدبلوماسي',
    }

    # إضافة معلومات المسؤول
    context.update(get_admin_context(request.user))

    return render(request, 'organizations/diplomatic_corps_list.html', context)


@regular_admin_required
def add_diplomatic_corps(request):
    """إضافة عضو جديد للسلك الدبلوماسي"""
    if request.method == 'POST':
        form = DiplomaticCorpsForm(request.POST)
        if form.is_valid():
            diplomat = form.save(commit=False)
            diplomat.created_by = request.user
            diplomat.save()
            messages.success(request, f'تم إضافة {diplomat.name} إلى السلك الدبلوماسي بنجاح.')
            return redirect('organizations:diplomatic_corps_list')
    else:
        form = DiplomaticCorpsForm()

    context = {
        'form': form,
        'title': 'إضافة عضو جديد للسلك الدبلوماسي',
        'submit_text': 'إضافة العضو',
    }

    return render(request, 'organizations/diplomatic_corps_form.html', context)


@login_required
def edit_diplomatic_corps(request, pk):
    """تعديل بيانات عضو السلك الدبلوماسي"""
    diplomat = get_object_or_404(DiplomaticCorps, pk=pk)

    if request.method == 'POST':
        form = DiplomaticCorpsForm(request.POST, instance=diplomat)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات {diplomat.name} بنجاح.')
            return redirect('organizations:diplomatic_corps_list')
    else:
        form = DiplomaticCorpsForm(instance=diplomat)

    context = {
        'form': form,
        'diplomat': diplomat,
        'title': f'تعديل بيانات {diplomat.name}',
        'submit_text': 'حفظ التغييرات',
    }

    return render(request, 'organizations/diplomatic_corps_form.html', context)


@login_required
def delete_diplomatic_corps(request, pk):
    """حذف عضو من السلك الدبلوماسي"""
    diplomat = get_object_or_404(DiplomaticCorps, pk=pk)

    if request.method == 'POST':
        diplomat_name = diplomat.name
        diplomat.delete()
        messages.success(request, f'تم حذف {diplomat_name} من السلك الدبلوماسي بنجاح.')
        return redirect('organizations:diplomatic_corps_list')

    context = {
        'diplomat': diplomat,
        'title': f'حذف {diplomat.name}',
    }

    return render(request, 'organizations/delete_diplomatic_corps.html', context)


@login_required
def export_diplomatic_corps(request):
    """تصدير السلك الدبلوماسي إلى Excel"""
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename="diplomatic_corps.xlsx"'

    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = 'السلك الدبلوماسي'

    # إعداد العناوين
    headers = ['الاسم', 'الرتبة', 'البلد/المهمة', 'نوع المهمة', 'تاريخ البداية', 'تاريخ النهاية', 'مدة الخدمة', 'الحالة', 'المنصب الحالي', 'اللغات', 'الهاتف', 'البريد الإلكتروني']
    for col_num, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col_num)
        cell.value = header
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')

    # إضافة البيانات
    diplomats = DiplomaticCorps.objects.all()
    for row_num, diplomat in enumerate(diplomats, 2):
        worksheet.cell(row=row_num, column=1, value=diplomat.name)
        worksheet.cell(row=row_num, column=2, value=diplomat.get_rank_display())
        worksheet.cell(row=row_num, column=3, value=diplomat.country)
        worksheet.cell(row=row_num, column=4, value=diplomat.get_mission_type_display())
        worksheet.cell(row=row_num, column=5, value=diplomat.start_date.strftime('%Y-%m-%d') if diplomat.start_date else '')
        worksheet.cell(row=row_num, column=6, value=diplomat.end_date.strftime('%Y-%m-%d') if diplomat.end_date else '')
        worksheet.cell(row=row_num, column=7, value=diplomat.get_service_duration())
        worksheet.cell(row=row_num, column=8, value=diplomat.get_status_display())
        worksheet.cell(row=row_num, column=9, value=diplomat.current_position or '')
        worksheet.cell(row=row_num, column=10, value=diplomat.languages or '')
        worksheet.cell(row=row_num, column=11, value=diplomat.phone or '')
        worksheet.cell(row=row_num, column=12, value=diplomat.email or '')

    workbook.save(response)
    return response


# ==================== الأعيان ====================

@regular_admin_required
def notables_list(request):
    """عرض قائمة الأعيان"""
    # إنشاء أعيان تجريبيين إذا لم يكونوا موجودين
    if not Notable.objects.exists():
        Notable.objects.create(
            name="الشيخ محمد ولد أحمد",
            title="شيخ قبيلة أولاد دليم",
            category="tribal_leader",
            tribe="أولاد دليم",
            region="تيرس زمور",
            phone="+22222345678",
            email="<EMAIL>",
            achievements="قائد مجتمعي معروف بحكمته وعدالته",
            status="active"
        )
        Notable.objects.create(
            name="الحاجة فاطمة بنت عبد الله",
            title="رئيسة جمعية نساء موريتانيا",
            category="social_leader",
            tribe="أولاد امبارك",
            region="نواكشوط",
            phone="+22222456789",
            email="<EMAIL>",
            achievements="ناشطة اجتماعية ومدافعة عن حقوق المرأة",
            status="active"
        )

    # تطبيق فلتر البيانات حسب المسؤول
    notables = filter_data_by_admin(Notable.objects.all(), request.user)

    # البحث
    search_query = request.GET.get('search', '')
    if search_query:
        notables = notables.filter(
            Q(name__icontains=search_query) |
            Q(title__icontains=search_query) |
            Q(tribe__icontains=search_query) |
            Q(current_position__icontains=search_query) |
            Q(achievements__icontains=search_query)
        )

    # الفلترة حسب الفئة
    category_filter = request.GET.get('category', '')
    if category_filter:
        notables = notables.filter(category=category_filter)

    # الفلترة حسب المنطقة
    region_filter = request.GET.get('region', '')
    if region_filter:
        notables = notables.filter(region=region_filter)

    # الفلترة حسب الحالة
    status_filter = request.GET.get('status', '')
    if status_filter:
        notables = notables.filter(status=status_filter)

    # الفلترة حسب القبيلة
    tribe_filter = request.GET.get('tribe', '')
    if tribe_filter:
        notables = notables.filter(tribe__icontains=tribe_filter)

    # إحصائيات
    stats = {
        'total': Notable.objects.count(),
        'active': Notable.objects.filter(status='active').count(),
        'inactive': Notable.objects.filter(status='inactive').count(),
        'deceased': Notable.objects.filter(status='deceased').count(),
        'tribal_leaders': Notable.objects.filter(category='tribal_leader').count(),
        'religious_leaders': Notable.objects.filter(category='religious_leader').count(),
    }

    # الحصول على قوائم للفلاتر
    tribes = Notable.objects.values_list('tribe', flat=True).distinct().exclude(tribe__isnull=True).exclude(tribe='').order_by('tribe')
    category_choices = Notable.CATEGORY_CHOICES
    region_choices = Notable.REGION_CHOICES
    status_choices = Notable.STATUS_CHOICES

    context = {
        'notables': notables,
        'stats': stats,
        'search_query': search_query,
        'category_filter': category_filter,
        'region_filter': region_filter,
        'status_filter': status_filter,
        'tribe_filter': tribe_filter,
        'tribes': tribes,
        'category_choices': category_choices,
        'region_choices': region_choices,
        'status_choices': status_choices,
        'title': 'الأعيان',
    }

    # إضافة معلومات المسؤول
    context.update(get_admin_context(request.user))

    return render(request, 'organizations/notables_list.html', context)


@regular_admin_required
def add_notable(request):
    """إضافة عين جديد"""
    if request.method == 'POST':
        form = NotableForm(request.POST)
        if form.is_valid():
            notable = form.save(commit=False)
            notable.created_by = request.user
            notable.save()
            messages.success(request, f'تم إضافة {notable.get_full_title()} إلى قائمة الأعيان بنجاح.')
            return redirect('organizations:notables_list')
    else:
        form = NotableForm()

    context = {
        'form': form,
        'title': 'إضافة عين جديد',
        'submit_text': 'إضافة العين',
    }

    return render(request, 'organizations/notable_form.html', context)


@login_required
def edit_notable(request, pk):
    """تعديل بيانات العين"""
    notable = get_object_or_404(Notable, pk=pk)

    if request.method == 'POST':
        form = NotableForm(request.POST, instance=notable)
        if form.is_valid():
            form.save()
            messages.success(request, f'تم تحديث بيانات {notable.get_full_title()} بنجاح.')
            return redirect('organizations:notables_list')
    else:
        form = NotableForm(instance=notable)

    context = {
        'form': form,
        'notable': notable,
        'title': f'تعديل بيانات {notable.get_full_title()}',
        'submit_text': 'حفظ التغييرات',
    }

    return render(request, 'organizations/notable_form.html', context)


@login_required
def delete_notable(request, pk):
    """حذف عين من القائمة"""
    notable = get_object_or_404(Notable, pk=pk)

    if request.method == 'POST':
        notable_name = notable.get_full_title()
        notable.delete()
        messages.success(request, f'تم حذف {notable_name} من قائمة الأعيان بنجاح.')
        return redirect('organizations:notables_list')

    context = {
        'notable': notable,
        'title': f'حذف {notable.get_full_title()}',
    }

    return render(request, 'organizations/delete_notable.html', context)


@login_required
def export_notables(request):
    """تصدير الأعيان إلى Excel"""
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename="notables.xlsx"'

    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = 'الأعيان'

    # إعداد العناوين
    headers = ['الاسم الكامل', 'اللقب', 'الفئة', 'المنطقة', 'القبيلة', 'العمر', 'الحالة', 'المنصب الحالي', 'الهاتف', 'البريد الإلكتروني', 'العنوان']
    for col_num, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col_num)
        cell.value = header
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')

    # إضافة البيانات
    notables = Notable.objects.all()
    for row_num, notable in enumerate(notables, 2):
        worksheet.cell(row=row_num, column=1, value=notable.name)
        worksheet.cell(row=row_num, column=2, value=notable.title or '')
        worksheet.cell(row=row_num, column=3, value=notable.get_category_display())
        worksheet.cell(row=row_num, column=4, value=notable.get_region_display())
        worksheet.cell(row=row_num, column=5, value=notable.tribe or '')
        worksheet.cell(row=row_num, column=6, value=notable.get_age() or '')
        worksheet.cell(row=row_num, column=7, value=notable.get_status_display())
        worksheet.cell(row=row_num, column=8, value=notable.current_position or '')
        worksheet.cell(row=row_num, column=9, value=notable.phone or '')
        worksheet.cell(row=row_num, column=10, value=notable.email or '')
        worksheet.cell(row=row_num, column=11, value=notable.address or '')

    workbook.save(response)
    return response


# ==================== Organization Management Views ====================

@staff_member_required
def add_organization(request):
    """إضافة مؤسسة جديدة"""
    if request.method == 'POST':
        name = request.POST.get('name')
        email = request.POST.get('email')
        phone = request.POST.get('phone')
        website = request.POST.get('website')
        address = request.POST.get('address')
        description = request.POST.get('description')
        organization_type = request.POST.get('organization_type', 'government')
        logo = request.FILES.get('logo')

        try:
            organization = Organization.objects.create(
                name=name,
                email=email,
                phone=phone,
                website=website,
                address=address,
                description=description,
                organization_type=organization_type,
                logo=logo
            )

            return JsonResponse({
                'success': True,
                'message': f'تم إضافة المؤسسة {organization.name} بنجاح'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'حدث خطأ أثناء إضافة المؤسسة: {str(e)}'
            })

    return JsonResponse({'success': False, 'message': 'طريقة طلب غير صحيحة'})


@staff_member_required
def edit_organization(request):
    """تعديل مؤسسة موجودة"""
    if request.method == 'POST':
        organization_id = request.POST.get('organization_id')

        try:
            organization = Organization.objects.get(pk=organization_id)

            organization.name = request.POST.get('name')
            organization.email = request.POST.get('email')
            organization.phone = request.POST.get('phone')
            organization.website = request.POST.get('website')
            organization.address = request.POST.get('address')
            organization.description = request.POST.get('description')

            # تحديث الشعار إذا تم رفع ملف جديد
            if 'logo' in request.FILES:
                organization.logo = request.FILES['logo']

            organization.save()

            return JsonResponse({
                'success': True,
                'message': f'تم تحديث المؤسسة {organization.name} بنجاح'
            })
        except Organization.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'المؤسسة غير موجودة'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'حدث خطأ أثناء تحديث المؤسسة: {str(e)}'
            })

    return JsonResponse({'success': False, 'message': 'طريقة طلب غير صحيحة'})


@staff_member_required
def delete_organization(request, pk):
    """حذف مؤسسة"""
    if request.method == 'POST':
        try:
            organization = Organization.objects.get(pk=pk)
            organization_name = organization.name
            organization.delete()

            return JsonResponse({
                'success': True,
                'message': f'تم حذف المؤسسة {organization_name} بنجاح'
            })
        except Organization.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': 'المؤسسة غير موجودة'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'حدث خطأ أثناء حذف المؤسسة: {str(e)}'
            })

    return JsonResponse({'success': False, 'message': 'طريقة طلب غير صحيحة'})


@staff_member_required
def bulk_delete_organizations(request):
    """حذف عدة مؤسسات"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            organization_ids = data.get('organization_ids', [])

            if not organization_ids:
                return JsonResponse({
                    'success': False,
                    'message': 'لم يتم تحديد أي مؤسسة للحذف'
                })

            # حذف المؤسسات المحددة
            deleted_count = Organization.objects.filter(pk__in=organization_ids).count()
            Organization.objects.filter(pk__in=organization_ids).delete()

            return JsonResponse({
                'success': True,
                'message': f'تم حذف {deleted_count} مؤسسة بنجاح'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'حدث خطأ أثناء الحذف: {str(e)}'
            })

    return JsonResponse({'success': False, 'message': 'طريقة طلب غير صحيحة'})


# ==================== رؤساء الأحزاب ====================

@login_required
@regular_admin_required
def party_leaders(request):
    """عرض قائمة رؤساء الأحزاب"""
    context = get_admin_context(request)

    # تطبيق فلترة البيانات حسب نوع المدير
    party_leaders_queryset = filter_data_by_admin(request, PartyLeader.objects.all())

    # البحث
    search_query = request.GET.get('search', '')
    if search_query:
        party_leaders_queryset = party_leaders_queryset.filter(
            Q(name__icontains=search_query) |
            Q(party_name__icontains=search_query) |
            Q(phone__icontains=search_query) |
            Q(email__icontains=search_query)
        )

    # الفلترة حسب الحالة
    status_filter = request.GET.get('status', '')
    if status_filter:
        party_leaders_queryset = party_leaders_queryset.filter(status=status_filter)

    # الفلترة حسب المنصب
    title_filter = request.GET.get('title', '')
    if title_filter:
        party_leaders_queryset = party_leaders_queryset.filter(title=title_filter)

    # الترتيب
    party_leaders_queryset = party_leaders_queryset.order_by('name')

    context.update({
        'party_leaders': party_leaders_queryset,
        'search_query': search_query,
        'status_filter': status_filter,
        'title_filter': title_filter,
        'status_choices': PartyLeader.STATUS_CHOICES,
        'title_choices': PartyLeader.TITLE_CHOICES,
        'total_count': party_leaders_queryset.count(),
    })

    return render(request, 'organizations/party_leaders.html', context)


@login_required
@regular_admin_required
def add_party_leader(request):
    """إضافة رئيس حزب جديد"""
    context = get_admin_context(request)

    if request.method == 'POST':
        form = PartyLeaderForm(request.POST, request.FILES)
        if form.is_valid():
            party_leader = form.save(commit=False)
            party_leader.created_by = request.user
            party_leader.save()
            messages.success(request, 'تم إضافة رئيس الحزب بنجاح!')
            return redirect('organizations:party_leaders')
    else:
        form = PartyLeaderForm()

    context.update({
        'form': form,
        'title': 'إضافة رئيس حزب جديد'
    })

    return render(request, 'organizations/add_party_leader.html', context)


@login_required
@regular_admin_required
def edit_party_leader(request, pk):
    """تعديل رئيس حزب"""
    context = get_admin_context(request)

    # تطبيق فلترة البيانات حسب نوع المدير
    party_leaders_queryset = filter_data_by_admin(request, PartyLeader.objects.all())
    party_leader = get_object_or_404(party_leaders_queryset, pk=pk)

    if request.method == 'POST':
        form = PartyLeaderForm(request.POST, request.FILES, instance=party_leader)
        if form.is_valid():
            form.save()
            messages.success(request, 'تم تحديث بيانات رئيس الحزب بنجاح!')
            return redirect('organizations:party_leaders')
    else:
        form = PartyLeaderForm(instance=party_leader)

    context.update({
        'form': form,
        'party_leader': party_leader,
        'title': f'تعديل بيانات {party_leader.name}'
    })

    return render(request, 'organizations/edit_party_leader.html', context)


@login_required
@regular_admin_required
def delete_party_leader(request, pk):
    """حذف رئيس حزب"""
    # تطبيق فلترة البيانات حسب نوع المدير
    party_leaders_queryset = filter_data_by_admin(request, PartyLeader.objects.all())
    party_leader = get_object_or_404(party_leaders_queryset, pk=pk)

    if request.method == 'POST':
        name = party_leader.name
        party_leader.delete()
        messages.success(request, f'تم حذف رئيس الحزب {name} بنجاح!')
        return redirect('organizations:party_leaders')

    context = get_admin_context(request)
    context.update({
        'party_leader': party_leader,
        'title': f'حذف {party_leader.name}'
    })

    return render(request, 'organizations/delete_party_leader.html', context)


@login_required
@regular_admin_required
def export_party_leaders(request):
    """تصدير رؤساء الأحزاب إلى Excel"""
    # تطبيق فلترة البيانات حسب نوع المدير
    party_leaders_queryset = filter_data_by_admin(request, PartyLeader.objects.all())

    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="party_leaders.xlsx"'

    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = 'رؤساء الأحزاب'

    # إعداد العناوين
    headers = [
        'الاسم الكامل', 'اسم الحزب', 'المنصب', 'رقم الهاتف', 'البريد الإلكتروني',
        'العنوان', 'سنة تأسيس الحزب', 'الأيديولوجية السياسية', 'مقر الحزب',
        'السيرة الذاتية', 'الإنجازات', 'الحالة', 'تاريخ الإضافة'
    ]

    for col_num, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col_num)
        cell.value = header
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')

    # إضافة البيانات
    for row_num, party_leader in enumerate(party_leaders_queryset, 2):
        worksheet.cell(row=row_num, column=1, value=party_leader.name)
        worksheet.cell(row=row_num, column=2, value=party_leader.party_name)
        worksheet.cell(row=row_num, column=3, value=party_leader.get_title_display())
        worksheet.cell(row=row_num, column=4, value=party_leader.phone)
        worksheet.cell(row=row_num, column=5, value=party_leader.email)
        worksheet.cell(row=row_num, column=6, value=party_leader.address)
        worksheet.cell(row=row_num, column=7, value=party_leader.party_founded_year)
        worksheet.cell(row=row_num, column=8, value=party_leader.party_ideology)
        worksheet.cell(row=row_num, column=9, value=party_leader.party_headquarters)
        worksheet.cell(row=row_num, column=10, value=party_leader.biography)
        worksheet.cell(row=row_num, column=11, value=party_leader.achievements)
        worksheet.cell(row=row_num, column=12, value=party_leader.get_status_display())
        worksheet.cell(row=row_num, column=13, value=party_leader.created_at.strftime('%Y-%m-%d'))

    workbook.save(response)
    return response


@login_required
@regular_admin_required
def import_party_leaders(request):
    """استيراد رؤساء الأحزاب من Excel"""
    context = get_admin_context(request)

    if request.method == 'POST':
        if 'file' not in request.FILES:
            messages.error(request, 'يرجى اختيار ملف للاستيراد')
            return redirect('organizations:party_leaders')

        file = request.FILES['file']

        if not file.name.endswith(('.xlsx', '.xls')):
            messages.error(request, 'يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)')
            return redirect('organizations:party_leaders')

        try:
            workbook = openpyxl.load_workbook(file)
            worksheet = workbook.active

            imported_count = 0
            errors = []

            # تخطي الصف الأول (العناوين)
            for row_num, row in enumerate(worksheet.iter_rows(min_row=2, values_only=True), start=2):
                try:
                    if not row[0]:  # إذا كان الاسم فارغ، تخطي الصف
                        continue

                    # إنشاء أو تحديث رئيس الحزب
                    party_leader, created = PartyLeader.objects.get_or_create(
                        name=row[0],
                        party_name=row[1] or '',
                        defaults={
                            'title': 'president',  # القيمة الافتراضية
                            'phone': row[3] or '',
                            'email': row[4] or '',
                            'address': row[5] or '',
                            'party_founded_year': row[6] if row[6] and str(row[6]).isdigit() else None,
                            'party_ideology': row[7] or '',
                            'party_headquarters': row[8] or '',
                            'biography': row[9] or '',
                            'achievements': row[10] or '',
                            'status': 'active',
                            'created_by': request.user,
                        }
                    )

                    if created:
                        imported_count += 1

                except Exception as e:
                    errors.append(f'الصف {row_num}: {str(e)}')

            if imported_count > 0:
                messages.success(request, f'تم استيراد {imported_count} رئيس حزب بنجاح!')

            if errors:
                for error in errors[:5]:  # عرض أول 5 أخطاء فقط
                    messages.warning(request, error)
                if len(errors) > 5:
                    messages.warning(request, f'وهناك {len(errors) - 5} أخطاء أخرى...')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء قراءة الملف: {str(e)}')

        return redirect('organizations:party_leaders')

    context.update({
        'title': 'استيراد رؤساء الأحزاب'
    })

    return render(request, 'organizations/import_party_leaders.html', context)


@login_required
@regular_admin_required
@require_POST
def bulk_delete_party_leaders(request):
    """حذف متعدد لرؤساء الأحزاب"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            party_leader_ids = data.get('party_leader_ids', [])

            if not party_leader_ids:
                return JsonResponse({
                    'success': False,
                    'message': 'لم يتم تحديد أي رئيس حزب للحذف'
                })

            # تطبيق فلترة البيانات حسب نوع المدير
            party_leaders_queryset = filter_data_by_admin(request, PartyLeader.objects.all())

            # التأكد من أن جميع المعرفات صحيحة وضمن صلاحيات المدير
            valid_ids = party_leaders_queryset.filter(pk__in=party_leader_ids).values_list('pk', flat=True)

            if len(valid_ids) != len(party_leader_ids):
                return JsonResponse({
                    'success': False,
                    'message': 'بعض رؤساء الأحزاب المحددة غير موجودة أو ليس لديك صلاحية للوصول إليها'
                })

            # حذف رؤساء الأحزاب
            deleted_count, _ = PartyLeader.objects.filter(pk__in=party_leader_ids).delete()

            return JsonResponse({
                'success': True,
                'message': f'تم حذف {deleted_count} رئيس حزب بنجاح'
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'حدث خطأ أثناء الحذف: {str(e)}'
            })

    return JsonResponse({'success': False, 'message': 'طريقة طلب غير صحيحة'})


# ==================== صفحة رؤساء الأحزاب العامة ====================

@login_required
def party_leaders_public(request):
    """صفحة عرض رؤساء الأحزاب العامة مع التصميم الملون"""
    # إنشاء بيانات تجريبية إذا لم تكن موجودة
    if PartyLeader.objects.count() == 0:
        user = request.user
        party_leaders_data = [
            {
                'name': 'سيادة الرئيس سيدي محمد علي محمد العبد',
                'party_name': 'حزب المسار',
                'title': 'president',
                'phone': '22123456',
                'email': '<EMAIL>',
                'party_founded_year': 2015,
                'party_ideology': 'ليبرالي',
                'status': 'active'
            },
            {
                'name': 'سيادة الرئيس د. السعد لوليد',
                'party_name': 'حزب غير محدد',
                'title': 'president',
                'phone': '22234567',
                'email': '<EMAIL>',
                'party_founded_year': 2018,
                'party_ideology': 'وسطي',
                'status': 'active'
            },
            {
                'name': 'سيادة الرئيس الشيخ بوي ولد شيخنا تقي الله',
                'party_name': 'حزب الوحدة والتنمية',
                'title': 'president',
                'phone': '22345678',
                'email': '<EMAIL>',
                'party_founded_year': 2012,
                'party_ideology': 'إسلامي',
                'status': 'active'
            }
        ]

        for data in party_leaders_data:
            PartyLeader.objects.get_or_create(
                name=data['name'],
                party_name=data['party_name'],
                defaults={**data, 'created_by': user}
            )

    # الحصول على جميع رؤساء الأحزاب
    party_leaders = PartyLeader.objects.filter(status='active').order_by('name')

    # البحث
    search_query = request.GET.get('search', '')
    if search_query:
        party_leaders = party_leaders.filter(
            Q(name__icontains=search_query) |
            Q(party_name__icontains=search_query) |
            Q(party_ideology__icontains=search_query)
        )

    context = {
        'title': 'رؤساء الأحزاب',
        'party_leaders': party_leaders,
        'search_query': search_query,
        'total_count': party_leaders.count(),
    }

    return render(request, 'organizations/party_leaders_public.html', context)


@login_required
@regular_admin_required
def download_party_leaders_template(request):
    """تحميل قالب Excel لاستيراد رؤساء الأحزاب"""
    from openpyxl.styles import PatternFill, Border, Side

    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="party_leaders_template.xlsx"'

    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = 'قالب رؤساء الأحزاب'

    # إعداد العناوين
    headers = [
        'الاسم الكامل', 'اسم الحزب', 'المنصب', 'رقم الهاتف', 'البريد الإلكتروني',
        'العنوان', 'سنة تأسيس الحزب', 'الأيديولوجية السياسية', 'مقر الحزب',
        'السيرة الذاتية', 'الإنجازات'
    ]

    for col_num, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col_num)
        cell.value = header
        cell.font = Font(bold=True, color='FFFFFF')
        cell.fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

    # إضافة بيانات نموذجية
    sample_data = [
        ['محمد أحمد علي', 'حزب التقدم', 'رئيس الحزب', '22123456', '<EMAIL>',
         'نواكشوط، موريتانيا', '2010', 'ليبرالي', 'شارع الاستقلال، نواكشوط',
         'سياسي محنك بخبرة 20 عاماً', 'قاد الحزب للفوز في انتخابات 2019'],
        ['فاطمة محمد', 'حزب الوحدة', 'رئيسة الحزب', '22234567', '<EMAIL>',
         'نواذيبو، موريتانيا', '2015', 'اشتراكي', 'حي الصداقة، نواذيبو',
         'ناشطة حقوقية ومدافعة عن حقوق المرأة', 'أول امرأة تترأس حزباً سياسياً']
    ]

    for row_num, row_data in enumerate(sample_data, 2):
        for col_num, value in enumerate(row_data, 1):
            cell = worksheet.cell(row=row_num, column=col_num)
            cell.value = value
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

    # تنسيق الأعمدة
    for col in worksheet.columns:
        max_length = 0
        column = col[0].column_letter
        for cell in col:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        worksheet.column_dimensions[column].width = adjusted_width

    # إضافة ملاحظات
    notes_row = len(sample_data) + 4
    worksheet.cell(row=notes_row, column=1, value='ملاحظات مهمة:')
    worksheet.cell(row=notes_row, column=1).font = Font(bold=True, color='FF0000')

    notes = [
        '1. الاسم الكامل واسم الحزب مطلوبان',
        '2. سنة التأسيس يجب أن تكون رقماً صحيحاً',
        '3. البريد الإلكتروني يجب أن يكون بصيغة صحيحة',
        '4. احذف هذه الصفوف النموذجية قبل الاستيراد'
    ]

    for i, note in enumerate(notes):
        worksheet.cell(row=notes_row + i + 1, column=1, value=note)
        worksheet.cell(row=notes_row + i + 1, column=1).font = Font(color='666666')

    workbook.save(response)
    return response