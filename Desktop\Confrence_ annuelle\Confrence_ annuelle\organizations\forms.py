from django import forms
from django.utils.translation import gettext_lazy as _
from .models import Organization, Invitation, Scholar, ScholarInvitation, ElectedOfficial, FormerMinister, DiplomaticCorps, Notable

class OrganizationForm(forms.ModelForm):
    """Form for creating and updating organizations"""
    class Meta:
        model = Organization
        fields = ['name', 'name_fr', 'address', 'email', 'phone', 'contact_person', 'website', 'description', 'participation_status', 'logo', 'latitude', 'longitude']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4}),
            'address': forms.Textarea(attrs={'rows': 3}),
            'latitude': forms.NumberInput(attrs={'step': '0.000001'}),
            'longitude': forms.NumberInput(attrs={'step': '0.000001'}),
        }

class InvitationForm(forms.ModelForm):
    """Form for sending invitation to an organization"""
    class Meta:
        model = Invitation
        fields = ['organization', 'subject', 'message']
        widgets = {
            'message': forms.Textarea(attrs={'rows': 6}),
        }

class InvitationUpdateForm(forms.ModelForm):
    """Form for updating an invitation"""
    class Meta:
        model = Invitation
        fields = ['subject', 'message', 'status']
        widgets = {
            'message': forms.Textarea(attrs={'rows': 6}),
            'status': forms.Select(attrs={'class': 'form-select'}),
        }

class BulkInvitationForm(forms.Form):
    """Form for sending invitation to multiple organizations"""
    organizations = forms.ModelMultipleChoiceField(
        queryset=Organization.objects.all(),
        label=_('Organizations'),
        widget=forms.SelectMultiple(attrs={'class': 'form-control', 'size': '10'})
    )
    subject = forms.CharField(
        label=_('Subject'),
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    message = forms.CharField(
        label=_('Message'),
        widget=forms.Textarea(attrs={'rows': 6, 'class': 'form-control'})
    )

class ContactForm(forms.Form):
    """Form for contacting an organization"""
    name = forms.CharField(label=_('Your Name'), max_length=100)
    email = forms.EmailField(label=_('Your Email'))
    subject = forms.CharField(label=_('Subject'), max_length=255)
    message = forms.CharField(label=_('Message'), widget=forms.Textarea(attrs={'rows': 6}))

class OrganizationFilterForm(forms.Form):
    """Form for filtering organizations"""
    name = forms.CharField(label=_('Name'), required=False)
    participation_status = forms.ChoiceField(
        label=_('Participation Status'),
        choices=[
            ('', _('All')),
            ('confirmed', _('Confirmed')),
            ('pending', _('Pending')),
            ('declined', _('Declined')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )


class ScholarInvitationForm(forms.ModelForm):
    """Form for sending invitation to a scholar"""
    class Meta:
        model = ScholarInvitation
        fields = ['scholar', 'subject', 'message']
        widgets = {
            'message': forms.Textarea(attrs={'rows': 6, 'class': 'form-control'}),
            'subject': forms.TextInput(attrs={'class': 'form-control'}),
            'scholar': forms.Select(attrs={'class': 'form-select'}),
        }
        labels = {
            'scholar': _('العالم'),
            'subject': _('موضوع الدعوة'),
            'message': _('نص الدعوة'),
        }


class BulkScholarInvitationForm(forms.Form):
    """Form for sending invitation to multiple scholars"""
    scholars = forms.ModelMultipleChoiceField(
        queryset=Scholar.objects.all(),
        label=_('العلماء'),
        widget=forms.SelectMultiple(attrs={'class': 'form-control', 'size': '10'})
    )
    subject = forms.CharField(
        label=_('موضوع الدعوة'),
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    message = forms.CharField(
        label=_('نص الدعوة'),
        widget=forms.Textarea(attrs={'rows': 6, 'class': 'form-control'})
    )


class ElectedOfficialForm(forms.ModelForm):
    """نموذج إضافة وتعديل المنتخبين"""
    class Meta:
        model = ElectedOfficial
        fields = [
            'name', 'position', 'region', 'party', 'phone', 'email',
            'status', 'start_date', 'end_date', 'notes'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الكامل'}),
            'position': forms.Select(attrs={'class': 'form-select'}),
            'region': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'المنطقة أو الدائرة'}),
            'party': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الحزب السياسي (اختياري)'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات إضافية'}),
        }
        labels = {
            'name': _('الاسم الكامل'),
            'position': _('المنصب'),
            'region': _('المنطقة/الدائرة'),
            'party': _('الحزب السياسي'),
            'phone': _('رقم الهاتف'),
            'email': _('البريد الإلكتروني'),
            'status': _('الحالة'),
            'start_date': _('تاريخ بداية المنصب'),
            'end_date': _('تاريخ نهاية المنصب'),
            'notes': _('ملاحظات'),
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and end_date <= start_date:
            raise forms.ValidationError('تاريخ نهاية المنصب يجب أن يكون بعد تاريخ البداية')

        return cleaned_data


class FormerMinisterForm(forms.ModelForm):
    """نموذج الوزراء السابقين"""
    class Meta:
        model = FormerMinister
        fields = ['name', 'ministry', 'government', 'start_date', 'end_date', 'phone', 'email', 'current_position', 'status', 'achievements', 'notes']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الكامل'}),
            'ministry': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الوزارة'}),
            'government': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم الحكومة'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'}),
            'current_position': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'المنصب الحالي'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'achievements': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'الإنجازات والمساهمات'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات إضافية'}),
        }
        labels = {
            'name': 'الاسم الكامل',
            'ministry': 'الوزارة',
            'government': 'الحكومة',
            'start_date': 'تاريخ بداية المنصب',
            'end_date': 'تاريخ نهاية المنصب',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'current_position': 'المنصب الحالي',
            'status': 'الحالة',
            'achievements': 'الإنجازات',
            'notes': 'ملاحظات',
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and end_date <= start_date:
            raise forms.ValidationError('تاريخ نهاية المنصب يجب أن يكون بعد تاريخ البداية')

        return cleaned_data


class DiplomaticCorpsForm(forms.ModelForm):
    """نموذج السلك الدبلوماسي"""
    class Meta:
        model = DiplomaticCorps
        fields = ['name', 'rank', 'country', 'mission_type', 'start_date', 'end_date', 'phone', 'email', 'current_position', 'status', 'languages', 'education', 'achievements', 'notes']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'form-control'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الكامل'}),
            'rank': forms.Select(attrs={'class': 'form-select'}),
            'country': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'البلد أو المهمة'}),
            'mission_type': forms.Select(attrs={'class': 'form-select'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'}),
            'current_position': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'المنصب الحالي'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'languages': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اللغات المتقنة'}),
            'education': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'المؤهلات العلمية'}),
            'achievements': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'الإنجازات الدبلوماسية'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات إضافية'}),
        }
        labels = {
            'name': 'الاسم الكامل',
            'rank': 'الرتبة الدبلوماسية',
            'country': 'البلد/المهمة',
            'mission_type': 'نوع المهمة',
            'start_date': 'تاريخ بداية المهمة',
            'end_date': 'تاريخ نهاية المهمة',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'current_position': 'المنصب الحالي',
            'status': 'الحالة',
            'languages': 'اللغات',
            'education': 'المؤهلات العلمية',
            'achievements': 'الإنجازات الدبلوماسية',
            'notes': 'ملاحظات',
        }

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and end_date <= start_date:
            raise forms.ValidationError('تاريخ نهاية المهمة يجب أن يكون بعد تاريخ البداية')

        return cleaned_data


class NotableForm(forms.ModelForm):
    """نموذج الأعيان"""
    class Meta:
        model = Notable
        fields = ['name', 'title', 'category', 'region', 'tribe', 'birth_year', 'phone', 'email', 'address', 'current_position', 'status', 'achievements', 'social_influence', 'education', 'family_info', 'notes']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الكامل'}),
            'title': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اللقب أو المنصب'}),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'region': forms.Select(attrs={'class': 'form-select'}),
            'tribe': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم القبيلة'}),
            'birth_year': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': 'سنة الميلاد', 'min': 1900, 'max': 2024}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'العنوان الكامل'}),
            'current_position': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'المنصب الحالي'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'achievements': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'الإنجازات والمساهمات'}),
            'social_influence': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'التأثير الاجتماعي'}),
            'education': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'المؤهلات العلمية'}),
            'family_info': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'معلومات عائلية'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات إضافية'}),
        }
        labels = {
            'name': 'الاسم الكامل',
            'title': 'اللقب/المنصب',
            'category': 'الفئة',
            'region': 'المنطقة',
            'tribe': 'القبيلة',
            'birth_year': 'سنة الميلاد',
            'phone': 'رقم الهاتف',
            'email': 'البريد الإلكتروني',
            'address': 'العنوان',
            'current_position': 'المنصب الحالي',
            'status': 'الحالة',
            'achievements': 'الإنجازات والمساهمات',
            'social_influence': 'التأثير الاجتماعي',
            'education': 'التعليم',
            'family_info': 'معلومات عائلية',
            'notes': 'ملاحظات',
        }

    def clean_birth_year(self):
        birth_year = self.cleaned_data.get('birth_year')
        if birth_year:
            from datetime import date
            current_year = date.today().year
            if birth_year > current_year:
                raise forms.ValidationError('سنة الميلاد لا يمكن أن تكون في المستقبل')
            if birth_year < 1900:
                raise forms.ValidationError('سنة الميلاد غير صحيحة')
        return birth_year