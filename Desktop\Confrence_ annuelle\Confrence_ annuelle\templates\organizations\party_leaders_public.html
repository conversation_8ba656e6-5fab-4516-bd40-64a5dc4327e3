{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .stats-card h3 {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stats-card p {
        margin: 0;
        opacity: 0.9;
    }
    
    .leader-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    .leader-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }
    
    .leader-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 15px 15px 0 0;
    }
    
    .leader-name {
        font-size: 1.2rem;
        font-weight: bold;
        margin: 0;
    }
    
    .leader-party {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0;
    }

    .leader-avatar {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .leader-avatar .avatar-img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
    }

    .leader-avatar .default-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid #dee2e6;
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .party-ideology {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.85rem;
        display: inline-block;
    }
    
    .search-container {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .search-input {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-4">
                <h1 class="display-4 mb-3">{{ title }}</h1>
                <p class="lead text-muted">إدارة وعرض بيانات رؤساء الأحزاب الموريتانية</p>
                <div class="mt-4">
                    <a href="{% url 'organizations:add_party_leader' %}" class="btn btn-primary me-2">
                        <i class="fas fa-plus"></i> إضافة رئيس حزب جديد
                    </a>
                    <a href="{% url 'organizations:export_party_leaders' %}" class="btn btn-success me-2">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </a>
                    <a href="{% url 'organizations:import_party_leaders' %}" class="btn btn-info me-2">
                        <i class="fas fa-file-import"></i> استيراد Excel
                    </a>
                    <a href="{% url 'organizations:download_party_leaders_template' %}" class="btn btn-outline-info me-2">
                        <i class="fas fa-download"></i> تحميل القالب
                    </a>
                    <button id="printBtn" class="btn btn-outline-primary me-2" title="طباعة القائمة">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <a href="{% url 'organizations:organization_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للمؤسسات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3>{{ total_count }}</h3>
                <p>رئيس حزب</p>
            </div>
        </div>
    </div>

    <!-- البحث والفلترة -->
    <div class="search-container">
        <div class="row">
            <div class="col-md-8 mb-3">
                <input type="text" id="searchInput" class="form-control search-input"
                       placeholder="البحث في أسماء رؤساء الأحزاب أو أسماء الأحزاب..."
                       value="{{ search_query }}">
            </div>
            <div class="col-md-2 mb-3">
                <button id="clearBtn" class="btn btn-outline-secondary w-100">
                    <i class="fas fa-times"></i> مسح
                </button>
            </div>
            <div class="col-md-2 mb-3">
                <button id="bulkDeleteBtn" class="btn btn-outline-danger w-100" style="display: none;">
                    <i class="fas fa-trash"></i> حذف المحدد
                </button>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="selectAll">
                    <label class="form-check-label" for="selectAll">
                        تحديد الكل
                    </label>
                    <span id="selectedCount" class="text-muted ms-2" style="display: none;"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة رؤساء الأحزاب -->
    <div class="row">
        {% if party_leaders %}
            {% for leader in party_leaders %}
            <div class="col-lg-6 col-xl-4 leader-item"
                 data-name="{{ leader.name|lower }}"
                 data-party="{{ leader.party_name|lower }}">
                <div class="card leader-card">
                    <div class="leader-header">
                        <div class="d-flex align-items-center">
                            <div class="form-check me-3">
                                <input class="form-check-input leader-checkbox" type="checkbox"
                                       value="{{ leader.pk }}" id="leader{{ leader.pk }}">
                            </div>
                            <div class="leader-avatar me-3">
                                {% if leader.profile_image %}
                                    <img src="{{ leader.profile_image.url }}" alt="{{ leader.name }}" class="avatar-img">
                                {% else %}
                                    <div class="default-avatar">
                                        <i class="fas fa-user-tie fa-2x text-primary"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="leader-name mb-1">{{ leader.name }}</h5>
                                <p class="leader-party mb-0">{{ leader.party_name }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <span class="party-ideology">
                                {% if leader.title == 'president' %}
                                    رئيس الحزب
                                {% elif leader.title == 'secretary_general' %}
                                    الأمين العام
                                {% elif leader.title == 'deputy_president' %}
                                    نائب الرئيس
                                {% else %}
                                    {{ leader.get_title_display }}
                                {% endif %}
                            </span>
                        </div>

                        {% if leader.party_ideology %}
                        <p class="mb-2">
                            <i class="fas fa-flag text-muted me-2"></i>
                            <strong>الأيديولوجية:</strong> {{ leader.party_ideology }}
                        </p>
                        {% endif %}

                        {% if leader.phone %}
                        <p class="mb-2">
                            <i class="fas fa-phone text-muted me-2"></i>
                            <strong>الهاتف:</strong> {{ leader.phone }}
                        </p>
                        {% endif %}

                        {% if leader.email %}
                        <p class="mb-2">
                            <i class="fas fa-envelope text-muted me-2"></i>
                            <strong>البريد:</strong> {{ leader.email }}
                        </p>
                        {% endif %}

                        {% if leader.party_founded_year %}
                        <p class="mb-2">
                            <i class="fas fa-calendar text-muted me-2"></i>
                            <strong>تأسس الحزب:</strong> {{ leader.party_founded_year }}
                        </p>
                        {% endif %}

                        <div class="mt-3 d-flex justify-content-between align-items-center">
                            <span class="status-badge status-active">نشط</span>
                            <div class="btn-group" role="group">
                                <a href="{% url 'organizations:edit_party_leader' leader.pk %}"
                                   class="btn btn-outline-primary btn-sm"
                                   title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button"
                                        class="btn btn-outline-danger btn-sm"
                                        onclick="confirmDelete({{ leader.pk }}, '{{ leader.name }}')"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بيانات رؤساء أحزاب</h5>
                    <p class="text-muted">لا توجد بيانات متاحة حالياً</p>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- رسالة عدم وجود نتائج -->
    <div id="noResults" class="text-center py-5" style="display: none;">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">لم يتم العثور على نتائج مطابقة لبحثك</h5>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف رئيس الحزب: <strong id="leaderName"></strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف الجماعي -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1" aria-labelledby="bulkDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkDeleteModalLabel">تأكيد الحذف الجماعي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف <strong id="bulkCount"></strong> من رؤساء الأحزاب المحددين؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="bulkDeleteForm" method="post" action="{% url 'organizations:bulk_delete_party_leaders' %}" style="display: inline;">
                    {% csrf_token %}
                    <input type="hidden" id="selectedIds" name="selected_ids">
                    <button type="submit" class="btn btn-danger">حذف الكل</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const clearBtn = document.getElementById('clearBtn');
    const printBtn = document.getElementById('printBtn');
    const noResults = document.getElementById('noResults');
    const leaderItems = Array.from(document.querySelectorAll('.leader-item'));
    const selectAllCheckbox = document.getElementById('selectAll');
    const leaderCheckboxes = document.querySelectorAll('.leader-checkbox');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const selectedCount = document.getElementById('selectedCount');

    // وظيفة البحث
    function filterLeaders() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        let visibleCount = 0;

        leaderItems.forEach(function(item) {
            const name = item.getAttribute('data-name');
            const party = item.getAttribute('data-party');

            if (name.includes(searchTerm) || party.includes(searchTerm)) {
                item.style.display = 'block';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });

        if (visibleCount === 0 && searchTerm !== '') {
            noResults.style.display = 'block';
        } else {
            noResults.style.display = 'none';
        }
    }

    // وظيفة مسح البحث
    function clearSearch() {
        searchInput.value = '';
        filterLeaders();
    }

    // وظيفة الطباعة
    function printList() {
        window.print();
    }

    // وظيفة تحديث عداد المحدد
    function updateSelectedCount() {
        const checkedBoxes = document.querySelectorAll('.leader-checkbox:checked');
        const count = checkedBoxes.length;

        if (count > 0) {
            selectedCount.textContent = `(${count} محدد)`;
            selectedCount.style.display = 'inline';
            bulkDeleteBtn.style.display = 'block';
        } else {
            selectedCount.style.display = 'none';
            bulkDeleteBtn.style.display = 'none';
        }

        // تحديث حالة تحديد الكل
        selectAllCheckbox.checked = count === leaderCheckboxes.length;
        selectAllCheckbox.indeterminate = count > 0 && count < leaderCheckboxes.length;
    }

    // وظيفة تحديد الكل
    function toggleSelectAll() {
        const isChecked = selectAllCheckbox.checked;
        leaderCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });
        updateSelectedCount();
    }

    // وظيفة الحذف الجماعي
    function bulkDelete() {
        const checkedBoxes = document.querySelectorAll('.leader-checkbox:checked');
        const ids = Array.from(checkedBoxes).map(cb => cb.value);

        if (ids.length === 0) {
            alert('يرجى تحديد عنصر واحد على الأقل للحذف');
            return;
        }

        document.getElementById('bulkCount').textContent = ids.length;
        document.getElementById('selectedIds').value = ids.join(',');

        const modal = new bootstrap.Modal(document.getElementById('bulkDeleteModal'));
        modal.show();
    }

    // ربط الأحداث
    searchInput.addEventListener('input', filterLeaders);
    clearBtn.addEventListener('click', clearSearch);
    printBtn.addEventListener('click', printList);
    selectAllCheckbox.addEventListener('change', toggleSelectAll);
    bulkDeleteBtn.addEventListener('click', bulkDelete);

    leaderCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
});

// وظيفة تأكيد الحذف الفردي
function confirmDelete(leaderId, leaderName) {
    document.getElementById('leaderName').textContent = leaderName;
    document.getElementById('deleteForm').action = `/organizations/party-leaders/${leaderId}/delete/`;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
{% endblock %}
