from django.db import models
from django.utils.translation import gettext_lazy as _

class Organization(models.Model):
    PARTICIPATION_CHOICES = (
        ('invited', _('Invited')),
        ('confirmed', _('Confirmed')),
        ('declined', _('Declined')),
        ('attended', _('Attended')),
    )

    ORGANIZATION_TYPE_CHOICES = (
        ('government', _('Government')),
        ('private', _('Private')),
        ('scholar', _('Scholar')),
    )
    #
    name = models.CharField(_('Name'), max_length=255)
    name_fr = models.CharField(_('French Name'), max_length=255, blank=True, null=True, help_text=_('Name of the organization in French'))
    address = models.TextField(_('Address'), blank=True, null=True)
    email = models.EmailField(_('Email'))
    phone = models.CharField(_('Phone'), max_length=20, blank=True, null=True)
    contact_person = models.CharField(_('Contact Person'), max_length=255, blank=True, null=True)
    website = models.URLField(_('Website'), blank=True, null=True)
    description = models.TextField(_('Description'), blank=True, null=True)
    participation_status = models.CharField(_('Participation Status'), max_length=20, choices=PARTICIPATION_CHOICES, default='invited')
    organization_type = models.CharField(_('Organization Type'), max_length=20, choices=ORGANIZATION_TYPE_CHOICES, default='private')
    logo = models.ImageField(_('Logo'), upload_to='organizations/logos/', blank=True, null=True)
    latitude = models.FloatField(_('Latitude'), blank=True, null=True)
    longitude = models.FloatField(_('Longitude'), blank=True, null=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Organization')
        verbose_name_plural = _('Organizations')
        ordering = ['name']

    def __str__(self):
        return self.name
        # Une fonction
    def get_status_color(self):
        """Return the appropriate color for the participation status"""
        status_colors = {
            'invited': 'warning',
            'confirmed': 'success',
            'declined': 'danger',
            'attended': 'info'
        }
        return status_colors.get(self.participation_status, 'secondary')


class Invitation(models.Model):
    STATUS_CHOICES = (
        ('draft', _('Draft')),
        ('sent', _('Sent')),
        ('cancelled', _('Cancelled')),
        ('responded', _('Responded')),
    )
# A une fonction de ma mature de
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='invitations')
    subject = models.CharField(_('Subject'), max_length=255)
    message = models.TextField(_('Message'))
    sent_at = models.DateTimeField(_('Sent At'), auto_now_add=True)
    is_sent = models.BooleanField(_('Is Sent'), default=False)
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='draft')
    response_status = models.CharField(_('Response Status'), max_length=20, blank=True, null=True)
    response_date = models.DateTimeField(_('Response Date'), blank=True, null=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Invitation')
        verbose_name_plural = _('Invitations')
        ordering = ['-sent_at']

    def __str__(self):
        return f"Invitation to {self.organization.name} on {self.sent_at.strftime('%Y-%m-%d')}"

    def send(self):
        """Mark invitation as sent"""
        self.is_sent = True
        self.status = 'sent'
        self.save()

    def cancel(self):
        """Cancel invitation"""
        self.status = 'cancelled'
        self.save()

    def mark_as_responded(self):
        """Mark invitation as responded"""
        self.status = 'responded'
        self.save()


class Scholar(models.Model):
    """نموذج العلماء والشخصيات الدينية"""

    TITLE_CHOICES = (
        ('sheikh', _('الشيخ')),
        ('doctor', _('الدكتور')),
        ('professor', _('الأستاذ')),
        ('imam', _('الإمام')),
        ('mufti', _('المفتي')),
        ('qadi', _('القاضي')),
        ('other', _('أخرى')),
    )

    PARTICIPATION_STATUS_CHOICES = (
        ('invited', _('مدعو')),
        ('confirmed', _('مؤكد')),
        ('declined', _('اعتذر')),
        ('attended', _('حضر')),
    )



    # المعلومات الأساسية
    title = models.CharField(_('اللقب'), max_length=20, choices=TITLE_CHOICES, default='sheikh')
    name = models.CharField(_('الاسم'), max_length=255)
    full_name = models.CharField(_('الاسم الكامل'), max_length=500, blank=True, null=True)

    # المعلومات الإضافية
    position = models.CharField(_('الوظيفة/المنصب'), max_length=255, blank=True, null=True)
    organization = models.CharField(_('الجهة/المؤسسة'), max_length=255, blank=True, null=True)
    country = models.CharField(_('البلد'), max_length=100, blank=True, null=True)
    city = models.CharField(_('المدينة'), max_length=100, blank=True, null=True)

    # معلومات الاتصال
    email = models.EmailField(_('البريد الإلكتروني'), blank=True, null=True)
    phone = models.CharField(_('الهاتف'), max_length=20, blank=True, null=True)
    website = models.URLField(_('الموقع الإلكتروني'), blank=True, null=True)

    # معلومات المشاركة
    participation_status = models.CharField(
        _('حالة المشاركة'),
        max_length=20,
        choices=PARTICIPATION_STATUS_CHOICES,
        default='invited'
    )

    # معلومات التقديم والعرض
    PRESENTATION_CHOICES = (
        ('none', _('لا يوجد')),
        ('paper', _('ورقة بحثية')),
        ('lecture', _('محاضرة')),
        ('workshop', _('ورشة عمل')),
    )

    presentation_type = models.CharField(
        _('نوع العرض'),
        max_length=20,
        choices=PRESENTATION_CHOICES,
        default='none',
        blank=True,
        null=True
    )
    presentation_title = models.CharField(_('عنوان العرض'), max_length=500, blank=True, null=True)

    # حقل التقديم/الطلب
    application_status = models.CharField(
        _('حالة التقديم'),
        max_length=100,
        blank=True,
        null=True,
        help_text=_('حالة طلب المشاركة أو التقديم')
    )

    # معلومات إضافية
    biography = models.TextField(_('السيرة الذاتية'), blank=True, null=True)
    specialization = models.CharField(_('التخصص'), max_length=255, blank=True, null=True)
    photo = models.ImageField(_('الصورة'), upload_to='scholars/photos/', blank=True, null=True)

    # معلومات النظام
    created_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('أنشئ بواسطة'))
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('عالم')
        verbose_name_plural = _('العلماء')
        ordering = ['name']

    def __str__(self):
        return f"{self.get_title_display()} {self.name}"

    def get_status_color(self):
        """إرجاع اللون المناسب لحالة المشاركة"""
        status_colors = {
            'invited': 'warning',
            'confirmed': 'success',
            'declined': 'danger',
            'attended': 'info'
        }
        return status_colors.get(self.participation_status, 'secondary')

    def get_full_title_name(self):
        """إرجاع الاسم الكامل مع اللقب"""
        return f"{self.get_title_display()} {self.full_name or self.name}"

    def get_presentation_display_short(self):
        """إرجاع عرض مختصر لنوع التقديم"""
        if self.presentation_type and self.presentation_type != 'none':
            return self.get_presentation_type_display()
        return 'لا يوجد'




class ElectedOfficial(models.Model):
    """نموذج المنتخبين"""
    POSITION_CHOICES = [
        ('mayor', 'عمدة'),
        ('deputy', 'نائب'),
        ('head', 'رئيس جهة'),
    ]

    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('retired', 'متقاعد'),
    ]

    name = models.CharField(max_length=200, verbose_name="الاسم الكامل")
    position = models.CharField(max_length=20, choices=POSITION_CHOICES, verbose_name="المنصب")
    region = models.CharField(max_length=100, verbose_name="المنطقة/الدائرة")
    party = models.CharField(max_length=100, blank=True, null=True, verbose_name="الحزب السياسي")
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الهاتف")
    email = models.EmailField(blank=True, null=True, verbose_name="البريد الإلكتروني")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name="الحالة")
    start_date = models.DateField(verbose_name="تاريخ بداية المنصب")
    end_date = models.DateField(blank=True, null=True, verbose_name="تاريخ نهاية المنصب")
    notes = models.TextField(blank=True, null=True, verbose_name="ملاحظات")
    created_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True, verbose_name="أنشئ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإضافة")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "منتخب"
        verbose_name_plural = "المنتخبون"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.get_position_display()}"

    def get_position_display_with_region(self):
        return f"{self.get_position_display()} - {self.region}"


class ScholarInvitation(models.Model):
    """نموذج دعوات العلماء"""
    STATUS_CHOICES = (
        ('draft', _('مسودة')),
        ('sent', _('مرسلة')),
        ('cancelled', _('ملغية')),
        ('responded', _('تم الرد')),
    )

    scholar = models.ForeignKey(Scholar, on_delete=models.CASCADE, related_name='invitations', verbose_name=_('العالم'))
    subject = models.CharField(_('الموضوع'), max_length=255)
    message = models.TextField(_('الرسالة'))
    sent_at = models.DateTimeField(_('تاريخ الإرسال'), auto_now_add=True)
    is_sent = models.BooleanField(_('تم الإرسال'), default=False)
    status = models.CharField(_('الحالة'), max_length=20, choices=STATUS_CHOICES, default='draft')
    response_status = models.CharField(_('حالة الرد'), max_length=20, blank=True, null=True)
    response_date = models.DateTimeField(_('تاريخ الرد'), blank=True, null=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    # حقول WhatsApp
    phone_number = models.CharField(_('رقم الهاتف'), max_length=20, blank=True, null=True)
    whatsapp_message = models.TextField(_('رسالة WhatsApp'), blank=True, null=True)
    whatsapp_url = models.URLField(_('رابط WhatsApp'), blank=True, null=True)

    class Meta:
        verbose_name = _('دعوة عالم')
        verbose_name_plural = _('دعوات العلماء')
        ordering = ['-sent_at']

    def __str__(self):
        return f"دعوة إلى {self.scholar.get_full_title_name()} في {self.sent_at.strftime('%Y-%m-%d')}"

    def send(self):
        """تحديد الدعوة كمرسلة"""
        self.is_sent = True
        self.status = 'sent'
        self.save()

    def cancel(self):
        """إلغاء الدعوة"""
        self.status = 'cancelled'
        self.save()

    def mark_as_responded(self):
        """تحديد الدعوة كتم الرد عليها"""
        self.status = 'responded'
        self.save()

    def get_whatsapp_url(self):
        """إنشاء رابط WhatsApp لإرسال الدعوة"""
        if not self.phone_number:
            return None

        import urllib.parse
        message = f"{self.subject}\n\n{self.message}"
        encoded_message = urllib.parse.quote(message)

        whatsapp_url = f"https://wa.me/{self.phone_number}?text={encoded_message}"

        # حفظ الرابط في قاعدة البيانات
        if not self.whatsapp_url:
            self.whatsapp_url = whatsapp_url
            self.save()

        return whatsapp_url


class FormerMinister(models.Model):
    """نموذج الوزراء السابقين"""

    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('deceased', 'متوفى'),
    ]

    name = models.CharField(max_length=200, verbose_name='الاسم الكامل')
    ministry = models.CharField(max_length=200, verbose_name='الوزارة')
    government = models.CharField(max_length=200, blank=True, null=True, verbose_name='الحكومة')
    start_date = models.DateField(verbose_name='تاريخ بداية المنصب')
    end_date = models.DateField(blank=True, null=True, verbose_name='تاريخ نهاية المنصب')
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم الهاتف')
    email = models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني')
    current_position = models.CharField(max_length=200, blank=True, null=True, verbose_name='المنصب الحالي')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='الحالة')
    achievements = models.TextField(blank=True, null=True, verbose_name='الإنجازات')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    created_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'وزير سابق'
        verbose_name_plural = 'الوزراء السابقون'
        ordering = ['-start_date', 'name']

    def __str__(self):
        return f"{self.name} - وزير {self.ministry}"

    def get_service_duration(self):
        """حساب مدة الخدمة"""
        if self.end_date:
            duration = self.end_date - self.start_date
            years = duration.days // 365
            months = (duration.days % 365) // 30
            if years > 0:
                return f"{years} سنة و {months} شهر"
            else:
                return f"{months} شهر"
        return "مستمر"


class DiplomaticCorps(models.Model):
    """نموذج السلك الدبلوماسي"""

    RANK_CHOICES = [
        ('ambassador', 'سفير'),
        ('consul_general', 'قنصل عام'),
        ('consul', 'قنصل'),
        ('first_secretary', 'سكرتير أول'),
        ('second_secretary', 'سكرتير ثاني'),
        ('third_secretary', 'سكرتير ثالث'),
        ('attache', 'ملحق'),
        ('counselor', 'مستشار'),
        ('minister_plenipotentiary', 'وزير مفوض'),
    ]

    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('retired', 'متقاعد'),
        ('transferred', 'منقول'),
    ]

    MISSION_TYPE_CHOICES = [
        ('embassy', 'سفارة'),
        ('consulate', 'قنصلية'),
        ('permanent_mission', 'بعثة دائمة'),
        ('international_org', 'منظمة دولية'),
        ('ministry', 'وزارة الخارجية'),
    ]

    name = models.CharField(max_length=200, verbose_name='الاسم الكامل')
    rank = models.CharField(max_length=30, choices=RANK_CHOICES, verbose_name='الرتبة الدبلوماسية')
    country = models.CharField(max_length=100, verbose_name='البلد/المهمة')
    mission_type = models.CharField(max_length=20, choices=MISSION_TYPE_CHOICES, verbose_name='نوع المهمة')
    start_date = models.DateField(verbose_name='تاريخ بداية المهمة')
    end_date = models.DateField(blank=True, null=True, verbose_name='تاريخ نهاية المهمة')
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم الهاتف')
    email = models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني')
    current_position = models.CharField(max_length=200, blank=True, null=True, verbose_name='المنصب الحالي')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='الحالة')
    achievements = models.TextField(blank=True, null=True, verbose_name='الإنجازات الدبلوماسية')
    languages = models.CharField(max_length=200, blank=True, null=True, verbose_name='اللغات')
    education = models.TextField(blank=True, null=True, verbose_name='المؤهلات العلمية')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    created_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'عضو السلك الدبلوماسي'
        verbose_name_plural = 'السلك الدبلوماسي'
        ordering = ['-start_date', 'name']

    def __str__(self):
        return f"{self.name} - {self.get_rank_display()}"

    def get_service_duration(self):
        """حساب مدة الخدمة الدبلوماسية"""
        if self.end_date:
            duration = self.end_date - self.start_date
            years = duration.days // 365
            months = (duration.days % 365) // 30
            if years > 0:
                return f"{years} سنة و {months} شهر"
            else:
                return f"{months} شهر"
        return "مستمر"

    def get_mission_display_full(self):
        """عرض المهمة كاملة"""
        return f"{self.get_mission_type_display()} - {self.country}"


class Notable(models.Model):
    """نموذج الأعيان"""

    CATEGORY_CHOICES = [
        ('tribal_leader', 'زعيم قبلي'),
        ('religious_leader', 'زعيم ديني'),
        ('business_leader', 'رجل أعمال'),
        ('intellectual', 'مثقف'),
        ('social_leader', 'زعيم اجتماعي'),
        ('traditional_leader', 'زعيم تقليدي'),
        ('community_leader', 'زعيم مجتمعي'),
        ('elder', 'كبير السن'),
        ('other', 'أخرى'),
    ]

    STATUS_CHOICES = [
        ('active', 'نشط'),
        ('inactive', 'غير نشط'),
        ('deceased', 'متوفى'),
    ]

    REGION_CHOICES = [
        ('nouakchott', 'نواكشوط'),
        ('nouadhibou', 'نواذيبو'),
        ('adrar', 'أدرار'),
        ('dakhlet_nouadhibou', 'داخلت نواذيبو'),
        ('trarza', 'ترارزة'),
        ('brakna', 'البراكنة'),
        ('assaba', 'العصابة'),
        ('gorgol', 'كوركول'),
        ('guidimaka', 'كيديماغا'),
        ('hodh_ech_chargui', 'الحوض الشرقي'),
        ('hodh_el_gharbi', 'الحوض الغربي'),
        ('inchiri', 'إنشيري'),
        ('tagant', 'تكانت'),
        ('tiris_zemmour', 'تيرس زمور'),
        ('other', 'أخرى'),
    ]

    name = models.CharField(max_length=200, verbose_name='الاسم الكامل')
    title = models.CharField(max_length=100, blank=True, null=True, verbose_name='اللقب/المنصب')
    category = models.CharField(max_length=30, choices=CATEGORY_CHOICES, verbose_name='الفئة')
    region = models.CharField(max_length=30, choices=REGION_CHOICES, verbose_name='المنطقة')
    tribe = models.CharField(max_length=100, blank=True, null=True, verbose_name='القبيلة')
    birth_year = models.IntegerField(blank=True, null=True, verbose_name='سنة الميلاد')
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name='رقم الهاتف')
    email = models.EmailField(blank=True, null=True, verbose_name='البريد الإلكتروني')
    address = models.TextField(blank=True, null=True, verbose_name='العنوان')
    current_position = models.CharField(max_length=200, blank=True, null=True, verbose_name='المنصب الحالي')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='الحالة')
    achievements = models.TextField(blank=True, null=True, verbose_name='الإنجازات والمساهمات')
    social_influence = models.TextField(blank=True, null=True, verbose_name='التأثير الاجتماعي')
    education = models.TextField(blank=True, null=True, verbose_name='التعليم')
    family_info = models.TextField(blank=True, null=True, verbose_name='معلومات عائلية')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    created_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'عين'
        verbose_name_plural = 'الأعيان'
        ordering = ['name']

    def __str__(self):
        if self.title:
            return f"{self.title} {self.name}"
        return self.name

    def get_age(self):
        """حساب العمر التقريبي"""
        if self.birth_year:
            from datetime import date
            current_year = date.today().year
            return current_year - self.birth_year
        return None

    def get_full_title(self):
        """الحصول على اللقب الكامل"""
        if self.title:
            return f"{self.title} {self.name}"
        return self.name
