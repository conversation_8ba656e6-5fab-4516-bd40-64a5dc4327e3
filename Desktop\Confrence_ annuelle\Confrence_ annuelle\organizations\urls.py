from django.urls import path, re_path
from . import views

app_name = 'organizations'

urlpatterns = [
    # Organization URLs
    path('', views.organization_list, name='organization_list'),
    path('government/', views.government_organizations, name='government_organizations'),
    path('government/invitations/', views.government_invitations, name='government_invitations'),
    path('consulting/', views.consulting_organizations, name='consulting_organizations'),
    path('scholars/', views.scholars, name='scholars'),
    path('create/', views.organization_create, name='organization_create'),
    path('<int:pk>/', views.organization_detail, name='organization_detail'),
    path('<int:pk>/update/', views.organization_update, name='organization_update'),
    path('<int:pk>/delete/', views.organization_delete, name='organization_delete'),
    path('<int:pk>/change-status/', views.change_organization_status, name='change_organization_status'),
    path('<int:pk>/contact/', views.contact_organization, name='contact_organization'),
    path('map/', views.organization_map, name='organization_map'),
    path('export-excel/', views.export_organizations_excel, name='export_organizations_excel'),

    # Invitation URLs
    path('invitations/', views.invitation_list, name='invitation_list'),
    path('invitations/<int:pk>/', views.invitation_detail, name='invitation_detail'),
    path('invitations/<int:pk>/update/', views.invitation_update, name='invitation_update'),
    path('invitations/<int:pk>/delete/', views.invitation_delete, name='invitation_delete'),
    path('invitations/<int:pk>/cancel/', views.invitation_cancel, name='invitation_cancel'),
    path('invitations/<int:pk>/send/', views.invitation_send, name='invitation_send'),
    path('send-invitation/', views.send_invitation, name='send_invitation'),
    path('send-bulk-invitation/', views.send_bulk_invitation, name='send_bulk_invitation'),
    path('bulk-invitation-delete/', views.bulk_invitation_delete, name='bulk_invitation_delete'),
    path('government-invitations/', views.government_invitations, name='government_invitations'),

    # Organization Management URLs
    path('add-organization/', views.add_organization, name='add_organization'),
    path('edit-organization/', views.edit_organization, name='edit_organization'),
    path('delete-organization/<int:pk>/', views.delete_organization, name='delete_organization'),
    path('bulk-delete-organizations/', views.bulk_delete_organizations, name='bulk_delete_organizations'),

    # Admin Dashboard
    path('admin-dashboard/', views.admin_organizations_dashboard, name='admin_organizations_dashboard'),
    path('custom-admin-dashboard/', views.custom_admin_dashboard, name='custom_admin_dashboard'),

    # Scholar URLs
    path('scholars-list/', views.scholar_list, name='scholar_list'),
    path('scholars-list/<int:pk>/', views.scholar_detail, name='scholar_detail'),
    path('scholars-list/create/', views.scholar_create, name='scholar_create'),
    path('scholars-list/<int:pk>/update/', views.scholar_update, name='scholar_update'),
    path('scholars-list/<int:pk>/delete/', views.scholar_delete, name='scholar_delete'),
    path('scholars-list/export-excel/', views.scholar_export_excel, name='scholar_export_excel'),
    path('scholars-list/export-csv/', views.scholar_export_csv, name='scholar_export_csv'),
    path('scholars-list/bulk-update-status/', views.scholar_bulk_update_status, name='scholar_bulk_update_status'),

    # Scholar Invitation URLs (WhatsApp)
    path('scholars-list/send-invitation/', views.send_scholar_invitation, name='send_scholar_invitation'),
    path('scholars-list/send-bulk-invitation/', views.send_bulk_scholar_invitation, name='send_bulk_scholar_invitation'),
    path('scholars-list/invitation-confirm/<int:pk>/', views.scholar_invitation_confirm, name='scholar_invitation_confirm'),
    path('scholars-list/bulk-invitation-confirm/', views.bulk_scholar_invitation_confirm, name='bulk_scholar_invitation_confirm'),

    # Scholar Email Invitation URLs
    path('scholars-list/send-email-invitation/', views.send_scholar_email_invitation, name='send_scholar_email_invitation'),
    path('scholars-list/send-bulk-email-invitation/', views.send_bulk_scholar_email_invitation, name='send_bulk_scholar_email_invitation'),

    # Official invitation URLs
    path('officials/send-whatsapp-invitation/', views.send_official_whatsapp_invitation, name='send_official_whatsapp_invitation'),
    path('officials/send-bulk-whatsapp-invitation/', views.send_bulk_official_whatsapp_invitation, name='send_bulk_official_whatsapp_invitation'),
    path('officials/send-email-invitation/', views.send_official_email_invitation, name='send_official_email_invitation'),
    path('officials/send-bulk-email-invitation/', views.send_bulk_official_email_invitation, name='send_bulk_official_email_invitation'),
    path('officials/bulk-invitation-confirm/', views.bulk_official_invitation_confirm, name='bulk_official_invitation_confirm'),

    # Former Minister invitation URLs
    path('ministers/send-whatsapp-invitation/', views.send_minister_whatsapp_invitation, name='send_minister_whatsapp_invitation'),
    path('ministers/send-bulk-whatsapp-invitation/', views.send_bulk_minister_whatsapp_invitation, name='send_bulk_minister_whatsapp_invitation'),
    path('ministers/send-email-invitation/', views.send_minister_email_invitation, name='send_minister_email_invitation'),
    path('ministers/send-bulk-email-invitation/', views.send_bulk_minister_email_invitation, name='send_bulk_minister_email_invitation'),
    path('ministers/bulk-invitation-confirm/', views.bulk_minister_invitation_confirm, name='bulk_minister_invitation_confirm'),

    # Diplomatic Corps invitation URLs
    path('diplomats/send-whatsapp-invitation/', views.send_diplomat_whatsapp_invitation, name='send_diplomat_whatsapp_invitation'),
    path('diplomats/send-email-invitation/', views.send_diplomat_email_invitation, name='send_diplomat_email_invitation'),

    # Notable invitation URLs
    path('notables/send-whatsapp-invitation/', views.send_notable_whatsapp_invitation, name='send_notable_whatsapp_invitation'),
    path('notables/send-email-invitation/', views.send_notable_email_invitation, name='send_notable_email_invitation'),

    # URLs للمنتخبين
    path('elected/', views.elected_officials_list, name='elected_officials_list'),
    path('elected/mayors/', views.mayors_list, name='mayors_list'),
    path('elected/deputies/', views.deputies_list, name='deputies_list'),
    path('elected/heads/', views.heads_list, name='heads_list'),
    path('elected/add/', views.add_elected_official, name='add_elected_official'),
    path('elected/<int:pk>/edit/', views.edit_elected_official, name='edit_elected_official'),
    path('elected/<int:pk>/delete/', views.delete_elected_official, name='delete_elected_official'),
    path('elected/export/', views.export_elected_officials, name='export_elected_officials'),

    # رؤساء الأحزاب
    path('party-leaders/', views.party_leaders_list, name='party_leaders_list'),

    # الوزراء السابقون
    path('former-ministers/', views.former_ministers_list, name='former_ministers_list'),
    path('former-ministers/add/', views.add_former_minister, name='add_former_minister'),
    path('former-ministers/<int:pk>/edit/', views.edit_former_minister, name='edit_former_minister'),
    path('former-ministers/<int:pk>/delete/', views.delete_former_minister, name='delete_former_minister'),
    path('former-ministers/export/', views.export_former_ministers, name='export_former_ministers'),

    # السلك الدبلوماسي
    path('diplomatic-corps/', views.diplomatic_corps_list, name='diplomatic_corps_list'),
    path('diplomatic-corps/add/', views.add_diplomatic_corps, name='add_diplomatic_corps'),
    path('diplomatic-corps/<int:pk>/edit/', views.edit_diplomatic_corps, name='edit_diplomatic_corps'),
    path('diplomatic-corps/<int:pk>/delete/', views.delete_diplomatic_corps, name='delete_diplomatic_corps'),
    path('diplomatic-corps/export/', views.export_diplomatic_corps, name='export_diplomatic_corps'),

    # الأعيان
    path('notables/', views.notables_list, name='notables_list'),
    path('notables/add/', views.add_notable, name='add_notable'),
    path('notables/<int:pk>/edit/', views.edit_notable, name='edit_notable'),
    path('notables/<int:pk>/delete/', views.delete_notable, name='delete_notable'),
    path('notables/export/', views.export_notables, name='export_notables'),

    # رؤساء الأحزاب
    path('party-leaders/', views.party_leaders, name='party_leaders'),
    path('party-leaders/public/', views.party_leaders_public, name='party_leaders_public'),
    path('party-leaders/add/', views.add_party_leader, name='add_party_leader'),
    path('party-leaders/<int:pk>/edit/', views.edit_party_leader, name='edit_party_leader'),
    path('party-leaders/<int:pk>/delete/', views.delete_party_leader, name='delete_party_leader'),
    path('party-leaders/export/', views.export_party_leaders, name='export_party_leaders'),
    path('party-leaders/import/', views.import_party_leaders, name='import_party_leaders'),
    path('party-leaders/template/', views.download_party_leaders_template, name='download_party_leaders_template'),
    path('party-leaders/bulk-delete/', views.bulk_delete_party_leaders, name='bulk_delete_party_leaders'),

]