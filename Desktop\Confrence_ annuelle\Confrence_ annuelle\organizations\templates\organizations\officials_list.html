{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .official-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }

    .official-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .official-photo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #007bff;
    }

    .official-title {
        color: #007bff;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .official-name {
        font-size: 1.1em;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }

    .official-position {
        color: #666;
        font-style: italic;
        margin-bottom: 5px;
    }

    .official-organization {
        color: #555;
        margin-bottom: 5px;
    }

    .official-contact {
        color: #777;
        font-size: 0.9em;
    }

    .filter-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .stats-section {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        font-size: 2em;
        font-weight: bold;
        display: block;
    }
</style>
{% endblock %}

{% block content %}
{% csrf_token %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-users-cog text-primary"></i>
                    {{ title }}
                </h1>
                <div>
                    <a href="{% url 'organizations:add_official' %}" class="btn btn-primary me-2">
                        <i class="fas fa-plus"></i> إضافة مسؤول جديد
                    </a>

                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'organizations:export_officials' %}">
                                <i class="fas fa-file-excel text-success"></i> تصدير Excel
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'organizations:download_officials_template' %}">
                                <i class="fas fa-download text-info"></i> تحميل قالب Excel
                            </a></li>
                        </ul>
                    </div>

                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-warning dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-upload"></i> استيراد
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="document.getElementById('importFileInput').click()">
                                <i class="fas fa-file-excel text-warning"></i> استيراد من Excel
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="stats-section">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <span class="stat-number">{{ total_count }}</span>
                            <span>إجمالي المسؤولين</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <span class="stat-number">{{ categories_count }}</span>
                            <span>عدد الفئات</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <span class="stat-number" id="selected-count">0</span>
                            <span>المحدد للحذف</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <button type="button" id="bulkDeleteBtn" onclick="bulkDelete()"
                                    class="btn btn-danger btn-sm" style="display: none;">
                                <i class="fas fa-trash"></i> حذف المحدد
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- مدخل ملف مخفي للاستيراد -->
            <input type="file" id="importFileInput" accept=".xlsx,.xls" style="display: none;" onchange="handleFileImport(this)">

            <!-- Filters -->
            <div class="filter-section">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">البحث</label>
                        <input type="text" class="form-control" name="search" value="{{ search_query }}"
                               placeholder="البحث في الأسماء والمناصب...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الفئة</label>
                        <select class="form-select" name="category">
                            <option value="">جميع الفئات</option>
                            {% for key, value in category_choices %}
                                <option value="{{ key }}" {% if category_filter == key %}selected{% endif %}>
                                    {{ value }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">اللقب</label>
                        <select class="form-select" name="title">
                            <option value="">جميع الألقاب</option>
                            {% for key, value in title_choices %}
                                <option value="{{ key }}" {% if title_filter == key %}selected{% endif %}>
                                    {{ value }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الترتيب</label>
                        <select class="form-select" name="sort">
                            <option value="name" {% if current_sort == 'name' %}selected{% endif %}>الاسم</option>
                            <option value="title" {% if current_sort == 'title' %}selected{% endif %}>اللقب</option>
                            <option value="category" {% if current_sort == 'category' %}selected{% endif %}>الفئة</option>
                            <option value="date" {% if current_sort == 'date' %}selected{% endif %}>تاريخ الإضافة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">تحديد الكل</label>
                        <div class="d-flex gap-2">
                            <button type="button" id="selectAllBtn" onclick="toggleSelectAll()" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-check-square"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>



            <!-- Officials List -->
            {% if categories_with_officials %}
                {% for officials_data, category_name in categories_with_officials %}
                    {% with officials=officials_data.0 officials_count=officials_data.1 %}
                    <div class="mb-4">
                        <h4 class="text-primary mb-3">
                            <i class="fas fa-building me-2"></i>
                            {{ category_name }}
                            <span class="badge bg-primary ms-2">{{ officials_count }}</span>
                        </h4>

                        <div class="row">
                            {% for official in officials %}
                            <div class="col-md-6 col-lg-4">
                                <div class="official-card">
                                    <div class="d-flex align-items-start">
                                        <div class="flex-shrink-0 me-2">
                                            <input type="checkbox" class="form-check-input official-checkbox"
                                                   name="selected_officials" value="{{ official.id }}"
                                                   id="official-{{ official.id }}">
                                        </div>
                                        <div class="flex-shrink-0">
                                            {% if official.photo %}
                                                <img src="{{ official.photo.url }}" alt="{{ official.get_full_title }}" class="official-photo">
                                            {% else %}
                                                <div class="official-photo d-flex align-items-center justify-content-center bg-primary text-white">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="official-title">{{ official.get_title_display }}</div>
                                            <div class="official-name">{{ official.get_full_title }}</div>
                                            {% if official.position %}
                                                <div class="official-position">{{ official.position }}</div>
                                            {% endif %}
                                            {% if official.organization %}
                                                <div class="official-organization">
                                                    <i class="fas fa-building text-muted"></i> {{ official.organization }}
                                                </div>
                                            {% endif %}
                                            {% if official.phone or official.email %}
                                                <div class="official-contact">
                                                    {% if official.phone %}
                                                        <i class="fas fa-phone text-muted"></i> {{ official.phone }}
                                                    {% endif %}
                                                    {% if official.email %}
                                                        <br><i class="fas fa-envelope text-muted"></i> {{ official.email }}
                                                    {% endif %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <div class="d-flex justify-content-between mb-2">
                                            <a href="{% url 'organizations:edit_official' official.pk %}"
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i> تعديل
                                            </a>
                                            <a href="{% url 'organizations:delete_official' official.pk %}"
                                               class="btn btn-sm btn-outline-danger"
                                               onclick="return confirm('هل أنت متأكد من حذف هذا المسؤول؟')">
                                                <i class="fas fa-trash"></i> حذف
                                            </a>
                                        </div>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar-plus me-1"></i>
                                            أضيف في: {{ official.created_at|date:"Y/m/d" }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endwith %}
                {% endfor %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>لا توجد نتائج</h4>
                    <p class="text-muted">لم يتم العثور على مسؤولين يطابقون معايير البحث المحددة.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد المسؤولين من Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'organizations:import_officials' %}" enctype="multipart/form-data">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="importFile" class="form-label">اختر ملف Excel</label>
                        <input type="file" class="form-control" id="importFile" name="file"
                               accept=".xlsx,.xls" required>
                        <div class="form-text">
                            يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد:</h6>
                        <ul class="mb-0">
                            <li>تأكد من أن الملف يحتوي على الأعمدة المطلوبة</li>
                            <li>الصف الأول يجب أن يحتوي على عناوين الأعمدة</li>
                            <li>يمكنك تحميل قالب الاستيراد للحصول على التنسيق الصحيح</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>استيراد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد المسؤولين من Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'organizations:import_officials' %}" enctype="multipart/form-data">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="importFile" class="form-label">اختر ملف Excel</label>
                        <input type="file" class="form-control" id="importFile" name="file"
                               accept=".xlsx,.xls" required>
                        <div class="form-text">
                            يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد:</h6>
                        <ul class="mb-0">
                            <li>تأكد من أن الملف يحتوي على الأعمدة المطلوبة</li>
                            <li>الصف الأول يجب أن يحتوي على عناوين الأعمدة</li>
                            <li>يمكنك تحميل قالب الاستيراد للحصول على التنسيق الصحيح</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>استيراد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.official-checkbox');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const selectedCountSpan = document.getElementById('selected-count');

    // إظهار/إخفاء أزرار الحذف الجماعي
    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.official-checkbox:checked');
        const count = checkedBoxes.length;

        if (selectedCountSpan) {
            selectedCountSpan.textContent = count;
        }

        if (count > 0) {
            bulkDeleteBtn.style.display = 'inline-block';
        } else {
            bulkDeleteBtn.style.display = 'none';
        }

        updateSelectAllButton();
    }

    // تحديث زر تحديد الكل
    function updateSelectAllButton() {
        const checkedCount = document.querySelectorAll('.official-checkbox:checked').length;
        const totalCount = checkboxes.length;

        if (selectAllBtn) {
            if (checkedCount === 0) {
                selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i>';
                selectAllBtn.className = 'btn btn-outline-secondary btn-sm';
                selectAllBtn.title = 'تحديد الكل';
            } else if (checkedCount === totalCount) {
                selectAllBtn.innerHTML = '<i class="fas fa-square"></i>';
                selectAllBtn.className = 'btn btn-warning btn-sm';
                selectAllBtn.title = 'إلغاء التحديد';
            } else {
                selectAllBtn.innerHTML = '<i class="fas fa-minus-square"></i>';
                selectAllBtn.className = 'btn btn-info btn-sm';
                selectAllBtn.title = `محدد ${checkedCount} من ${totalCount}`;
            }
        }
    }

    // إضافة مستمع للأحداث لكل checkbox
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    // تحديث الحالة الأولية
    updateBulkActions();

});

// وظيفة تحديد/إلغاء تحديد الكل
function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('.official-checkbox');
    const checkedCount = document.querySelectorAll('.official-checkbox:checked').length;
    const shouldCheck = checkedCount === 0;

    checkboxes.forEach(checkbox => {
        checkbox.checked = shouldCheck;
    });

    // تحديث العدادات والأزرار
    const event = new Event('change');
    if (checkboxes.length > 0) {
        checkboxes[0].dispatchEvent(event);
    }
}

// وظيفة تحديد/إلغاء تحديد الكل
function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('.official-checkbox');
    const checkedCount = document.querySelectorAll('.official-checkbox:checked').length;
    const shouldCheck = checkedCount === 0;

    checkboxes.forEach(checkbox => {
        checkbox.checked = shouldCheck;
    });

    // تحديث العدادات والأزرار
    const event = new Event('change');
    if (checkboxes.length > 0) {
        checkboxes[0].dispatchEvent(event);
    }
}

// وظيفة الحذف الجماعي
function bulkDelete() {
    const checkedBoxes = document.querySelectorAll('.official-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('يرجى اختيار مسؤولين للحذف');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف ${checkedBoxes.length} مسؤول؟`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "organizations:bulk_delete_officials" %}';

        // إضافة CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                         document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         '{{ csrf_token }}';
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
        }

        // إضافة IDs المحددة
        checkedBoxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'official_ids';
            input.value = checkbox.value;
            form.appendChild(input);
        });

        document.body.appendChild(form);
        form.submit();
    }
}

// وظيفة التعامل مع استيراد الملفات
function handleFileImport(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];
        const fileName = file.name;
        const fileSize = (file.size / 1024 / 1024).toFixed(2);

        // التحقق من نوع الملف
        if (!fileName.match(/\.(xlsx|xls)$/i)) {
            alert('يرجى اختيار ملف Excel صالح (.xlsx أو .xls)');
            input.value = '';
            return;
        }

        // التحقق من حجم الملف
        if (file.size > 10 * 1024 * 1024) {
            alert('حجم الملف كبير جداً. يرجى اختيار ملف أقل من 10 ميجابايت');
            input.value = '';
            return;
        }

        // تأكيد الاستيراد
        if (confirm(`هل تريد استيراد الملف: ${fileName} (${fileSize} MB)؟`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{% url "organizations:import_officials" %}';
            form.enctype = 'multipart/form-data';

            // إضافة CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                             '{{ csrf_token }}';
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrfmiddlewaretoken';
                csrfInput.value = csrfToken;
                form.appendChild(csrfInput);
            }

            // إضافة الملف
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.name = 'file';
            fileInput.files = input.files;
            form.appendChild(fileInput);

            document.body.appendChild(form);
            form.submit();
        } else {
            input.value = '';
        }
    }
}
</script>
{% endblock %}
