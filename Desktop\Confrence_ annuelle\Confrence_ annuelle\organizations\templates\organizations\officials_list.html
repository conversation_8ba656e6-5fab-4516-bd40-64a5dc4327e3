{% extends 'base.html' %}
{% load static %}
{% load i18n %}


{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<!-- تحديث فوري للتخزين المؤقت -->
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<style>
    .officials-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .officials-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .officials-title {
        color: #2c3e50;
        font-size: 2.5rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    .officials-subtitle {
        color: #7f8c8d;
        text-align: center;
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .search-filters {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .category-section {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        margin-bottom: 2rem;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .category-header {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        padding: 1.5rem 2rem;
        font-size: 1.4rem;
        font-weight: 600;
        text-align: center;
        position: relative;
    }

    .category-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.1) 75%);
        background-size: 20px 20px;
    }

    .officials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
        padding: 2rem;
    }

    .official-card {
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: 1px solid rgba(52, 152, 219, 0.2);
        position: relative;
        overflow: hidden;
    }

    .official-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
    }

    .official-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-header-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .card-actions {
        display: flex;
        gap: 0.5rem;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .official-card:hover .card-actions {
        opacity: 1;
    }

    .card-actions .btn {
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    .card-actions .btn:hover {
        transform: scale(1.1);
    }

    .card-footer-info {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }

    .official-checkbox {
        transform: scale(1.2);
    }

    .official-title {
        color: #2c3e50;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }

    .official-position {
        color: #7f8c8d;
        font-size: 0.95rem;
        margin-bottom: 1rem;
        font-style: italic;
    }

    .official-contact {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .contact-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #5a6c7d;
        font-size: 0.9rem;
    }

    .contact-item i {
        width: 16px;
        color: #3498db;
    }

    .stats-bar {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 1rem 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;

    }

    .stats-info {
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    .stats-item {
        display: flex;
        align-items: center;
        color: #2c3e50;
        font-weight: 600;
        white-space: nowrap;
    }

    .export-btn {
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .export-btn:hover {
        background: linear-gradient(135deg, #229954, #27ae60);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        color: white;
        text-decoration: none;
    }

    .action-buttons {
        display: flex !important;
        align-items: center;
        gap: 0.5rem;
        margin-left: auto;
        flex-shrink: 0;
        min-width: 300px;
    }

    .btn-success {
        background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
        border: none !important;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        color: white !important;
        text-decoration: none !important;
        display: inline-block !important;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #229954, #27ae60);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
    }

    .btn-info {
        background: linear-gradient(135deg, #3498db, #2980b9) !important;
        border: none !important;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        color: white !important;
    }

    .btn-info:hover {
        background: linear-gradient(135deg, #2980b9, #1f4e79);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    .btn-danger {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        background: linear-gradient(135deg, #c0392b, #a93226);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
    }

    .no-results {
        text-align: center;
        padding: 3rem;
        color: #7f8c8d;
        font-size: 1.1rem;
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #3498db, #2980b9);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #2980b9, #1f4e79);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    @media (max-width: 768px) {
        .officials-grid {
            grid-template-columns: 1fr;
            padding: 1rem;
        }

        .officials-title {
            font-size: 2rem;
        }

        .stats-bar {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .stats-info {
            flex-direction: column;
            gap: 0.5rem;
        }

        .action-buttons {
            justify-content: center;
            flex-wrap: wrap;
        }
    }
</style>
{% endblock %}

{% block content %}
{% csrf_token %}

<div class="officials-container">
    <div class="container">
        <!-- Header -->
        <div class="officials-header">
            <h1 class="officials-title">
                <i class="fas fa-users-cog me-3"></i>
                {{ title }}
            </h1>
            <p class="officials-subtitle">
                قائمة شاملة بالمسؤولين الرسميين مصنفة حسب الوزارات والمؤسسات
            </p>

            <!-- تنبيه للأزرار -->
            <div style="background: linear-gradient(45deg, #ff6b6b, #ee5a24); color: white; padding: 15px; margin: 15px 0; border-radius: 10px; text-align: center; animation: pulse 2s infinite; box-shadow: 0 4px 15px rgba(255,107,107,0.4);">
                <i class="fas fa-arrow-down me-2"></i>
                <strong>استخدم الأزرار أدناه لإدارة المسؤولين الرسميين</strong>
                <i class="fas fa-arrow-down ms-2"></i>
            </div>

            <!-- تنبيه إضافي كبير -->
            <div style="background: #dc3545; color: white; padding: 25px; margin: 20px 0; border-radius: 15px; text-align: center; font-size: 24px; font-weight: bold; border: 5px solid #fff; box-shadow: 0 10px 30px rgba(220,53,69,0.5);">
                🚨 إذا لم تر الأزرار أدناه، اضغط Ctrl+F5 لتحديث الصفحة 🚨
            </div>
        </div>

        <!-- Action Buttons - مبسطة وواضحة -->
        <div style="background: #ff0000; padding: 50px; margin: 20px 0; border-radius: 15px; text-align: center; border: 10px solid #000000; position: relative; z-index: 9999;">
            <h1 style="color: #ffffff; margin-bottom: 25px; font-weight: bold; font-size: 36px; text-shadow: 2px 2px 4px #000;">
                🛠️ أدوات إدارة المسؤولين الرسميين - تحديث جديد
            </h1>

            <div style="display: block; text-align: center; margin-top: 30px;">
                <!-- زر إضافة مسؤول -->
                <a href="{% url 'organizations:add_official' %}"
                   style="background: #28a745; color: white; padding: 25px 50px; text-decoration: none; border-radius: 15px; font-size: 24px; font-weight: bold; display: block; margin: 15px auto; max-width: 400px; border: 5px solid #fff; box-shadow: 0 10px 20px rgba(0,0,0,0.3);">
                    ➕ إضافة مسؤول جديد
                </a>

                <!-- زر تصدير -->
                <a href="{% url 'organizations:export_officials' %}"
                   style="background: #007bff; color: white; padding: 25px 50px; text-decoration: none; border-radius: 15px; font-size: 24px; font-weight: bold; display: block; margin: 15px auto; max-width: 400px; border: 5px solid #fff; box-shadow: 0 10px 20px rgba(0,0,0,0.3);">
                    📊 تصدير إلى Excel
                </a>

                <!-- زر تحميل قالب -->
                <a href="{% url 'organizations:download_officials_template' %}"
                   style="background: #17a2b8; color: white; padding: 25px 50px; text-decoration: none; border-radius: 15px; font-size: 24px; font-weight: bold; display: block; margin: 15px auto; max-width: 400px; border: 5px solid #fff; box-shadow: 0 10px 20px rgba(0,0,0,0.3);">
                    📥 تحميل قالب Excel
                </a>

                <!-- زر استيراد -->
                <button type="button" onclick="document.getElementById('importFileInput').click()"
                        style="background: #ffc107; color: #212529; padding: 25px 50px; border: 5px solid #fff; border-radius: 15px; font-size: 24px; font-weight: bold; cursor: pointer; display: block; margin: 15px auto; max-width: 400px; box-shadow: 0 10px 20px rgba(0,0,0,0.3);">
                    📤 استيراد من Excel
                </button>
            </div>

            <!-- أزرار إضافية للحذف الجماعي -->
            <div style="margin-top: 20px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                <button type="button" id="toggleSelectAll" onclick="toggleSelectAll()"
                        style="background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; cursor: pointer; margin-right: 10px;">
                    ☑️ تحديد الكل
                </button>

                <button type="button" id="bulkDeleteBtn" onclick="bulkDelete()" style="display: none;
                        background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; cursor: pointer;">
                    🗑️ حذف المحدد
                </button>
            </div>
            </div>

            <!-- مدخل ملف مخفي للاستيراد -->
            <input type="file" id="importFileInput" accept=".xlsx,.xls" style="display: none;" onchange="handleFileImport(this)">
        </div>

        <!-- Search and Filters -->
        <div class="search-filters">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search_query }}" placeholder="البحث في الأسماء والمناصب...">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">الفئة</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">جميع الفئات</option>
                        {% for key, value in category_choices %}
                            <option value="{{ key }}" {% if category_filter == key %}selected{% endif %}>
                                {{ value }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="title" class="form-label">اللقب</label>
                    <select class="form-select" id="title" name="title">
                        <option value="">جميع الألقاب</option>
                        {% for key, value in title_choices %}
                            <option value="{{ key }}" {% if title_filter == key %}selected{% endif %}>
                                {{ value }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Stats -->
        <div class="stats-bar">
            <div class="stats-info">
                <div class="stats-item">
                    <i class="fas fa-users me-2"></i>
                    إجمالي المسؤولين: {{ total_count }}
                </div>
                <div class="stats-item">
                    <i class="fas fa-layer-group me-2"></i>
                    الفئات: {{ categories_count }}
                </div>
            </div>
        </div>



        <!-- Officials by Category -->
        {% if categories_with_officials %}
            {% for officials_data, category_name in categories_with_officials %}
                {% with officials=officials_data.0 officials_count=officials_data.1 %}
                <div class="category-section">
                    <div class="category-header">
                        <i class="fas fa-building me-3"></i>
                        {{ category_name }}
                        <span class="badge bg-light text-dark ms-3">{{ officials_count }}</span>
                    </div>
                    <div class="officials-grid">
                        {% for official in officials %}
                            <div class="official-card">
                                <div class="card-header-actions">
                                    <input type="checkbox" class="form-check-input official-checkbox"
                                           value="{{ official.id }}" style="display: inline-block; margin-left: 10px; transform: scale(1.5);">
                                    <div class="card-actions">
                                        <a href="{% url 'organizations:edit_official' official.id %}"
                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'organizations:delete_official' official.id %}"
                                           class="btn btn-sm btn-outline-danger" title="حذف"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا المسؤول؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="official-title">{{ official.get_full_title }}</div>
                                <div class="official-position">{{ official.position }}</div>
                                <div class="official-contact">
                                    {% if official.phone %}
                                        <div class="contact-item">
                                            <i class="fas fa-phone"></i>
                                            <span>{{ official.phone }}</span>
                                        </div>
                                    {% endif %}
                                    {% if official.email %}
                                        <div class="contact-item">
                                            <i class="fas fa-envelope"></i>
                                            <span>{{ official.email }}</span>
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="card-footer-info">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-plus me-1"></i>
                                        أضيف في: {{ official.created_at|date:"Y/m/d" }}
                                    </small>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
                {% endwith %}
            {% endfor %}
        {% else %}
            <div class="no-results">
                <i class="fas fa-search fa-3x mb-3"></i>
                <h4>لا توجد نتائج</h4>
                <p>لم يتم العثور على مسؤولين يطابقون معايير البحث المحددة.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد المسؤولين من Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'organizations:import_officials' %}" enctype="multipart/form-data">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="importFile" class="form-label">اختر ملف Excel</label>
                        <input type="file" class="form-control" id="importFile" name="file"
                               accept=".xlsx,.xls" required>
                        <div class="form-text">
                            يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد:</h6>
                        <ul class="mb-0">
                            <li>تأكد من أن الملف يحتوي على الأعمدة المطلوبة</li>
                            <li>الصف الأول يجب أن يحتوي على عناوين الأعمدة</li>
                            <li>يمكنك تحميل قالب الاستيراد للحصول على التنسيق الصحيح</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>استيراد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// فرض تحديث الصفحة إذا لم تظهر الأزرار
window.addEventListener('load', function() {
    const buttonsContainer = document.querySelector('[style*="أدوات إدارة المسؤولين الرسميين"]');
    if (!buttonsContainer) {
        console.log('الأزرار غير موجودة - سيتم إعادة تحميل الصفحة');
        setTimeout(() => {
            window.location.reload(true);
        }, 1000);
    }
});

document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.official-checkbox');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const selectAllBtn = document.getElementById('selectAllBtn');

    // إظهار/إخفاء أزرار الحذف الجماعي
    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.official-checkbox:checked');
        if (checkedBoxes.length > 0) {
            bulkDeleteBtn.style.display = 'inline-block';
        } else {
            bulkDeleteBtn.style.display = 'none';
        }
    }

    // إضافة مستمع للأحداث لكل checkbox
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActions();
            updateToggleButton();
        });
    });

    // تحديث زر تحديد الكل
    function updateToggleButton() {
        const checkboxes = document.querySelectorAll('.official-checkbox');
        const toggleBtn = document.getElementById('toggleSelectAll');
        const checkedCount = document.querySelectorAll('.official-checkbox:checked').length;

        if (checkedCount === 0) {
            toggleBtn.textContent = '☑️ تحديد الكل';
            toggleBtn.style.background = '#6c757d';
        } else if (checkedCount === checkboxes.length) {
            toggleBtn.textContent = '❌ إلغاء التحديد';
            toggleBtn.style.background = '#dc3545';
        } else {
            toggleBtn.textContent = `☑️ تحديد الكل (${checkedCount} محدد)`;
            toggleBtn.style.background = '#ffc107';
        }
    }

    // الحذف الجماعي
    bulkDeleteBtn.addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.official-checkbox:checked');
        if (checkedBoxes.length === 0) {
            alert('يرجى اختيار مسؤولين للحذف');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف ${checkedBoxes.length} مسؤول؟`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{% url "organizations:bulk_delete_officials" %}';

            // إضافة CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);

            // إضافة IDs المحددة
            checkedBoxes.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'official_ids';
                input.value = checkbox.value;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
        }
    });

    // إضافة زر تحديد الكل (اختياري)
    const statsBar = document.querySelector('.stats-bar');
    if (statsBar && checkboxes.length > 0) {
        const selectAllContainer = document.createElement('div');
        selectAllContainer.className = 'select-all-container';
        selectAllContainer.innerHTML = `
            <button type="button" class="btn btn-sm btn-outline-secondary" id="toggleSelectAll">
                <i class="fas fa-check-square me-1"></i>تحديد الكل
            </button>
        `;

        const actionButtons = statsBar.querySelector('.action-buttons');
        if (actionButtons) {
            actionButtons.insertBefore(selectAllContainer, actionButtons.firstChild);
        }

        // وظيفة تحديد/إلغاء تحديد الكل
        const toggleSelectAllBtn = document.getElementById('toggleSelectAll');
        let allSelected = false;

        toggleSelectAllBtn.addEventListener('click', function() {
            allSelected = !allSelected;
            checkboxes.forEach(checkbox => {
                checkbox.checked = allSelected;
                checkbox.style.display = allSelected ? 'inline-block' : 'none';
            });

            if (allSelected) {
                toggleSelectAllBtn.innerHTML = '<i class="fas fa-square me-1"></i>إلغاء التحديد';
                toggleSelectAllBtn.className = 'btn btn-sm btn-warning';
            } else {
                toggleSelectAllBtn.innerHTML = '<i class="fas fa-check-square me-1"></i>تحديد الكل';
                toggleSelectAllBtn.className = 'btn btn-sm btn-outline-secondary';
            }

            updateBulkActions();
        });
    }
});

// وظيفة التعامل مع استيراد الملفات
function handleFileImport(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];
        const fileName = file.name;
        const fileSize = (file.size / 1024 / 1024).toFixed(2); // بالميجابايت

        // التحقق من نوع الملف
        if (!fileName.match(/\.(xlsx|xls)$/i)) {
            alert('يرجى اختيار ملف Excel صالح (.xlsx أو .xls)');
            input.value = '';
            return;
        }

        // التحقق من حجم الملف (أقل من 10 ميجابايت)
        if (file.size > 10 * 1024 * 1024) {
            alert('حجم الملف كبير جداً. يرجى اختيار ملف أقل من 10 ميجابايت');
            input.value = '';
            return;
        }

        // تأكيد الاستيراد
        if (confirm(`هل تريد استيراد الملف: ${fileName} (${fileSize} MB)؟`)) {
            // إنشاء form وإرساله
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{% url "organizations:import_officials" %}';
            form.enctype = 'multipart/form-data';

            // إضافة CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                             document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrfmiddlewaretoken';
                csrfInput.value = csrfToken;
                form.appendChild(csrfInput);
            }

            // إضافة الملف
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.name = 'file';
            fileInput.files = input.files;
            form.appendChild(fileInput);

            // إضافة loading indicator
            const loadingDiv = document.createElement('div');
            loadingDiv.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;">
                    <div style="background: white; padding: 30px; border-radius: 10px; text-align: center;">
                        <i class="fas fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                        <h4>جاري استيراد البيانات...</h4>
                        <p>يرجى الانتظار...</p>
                    </div>
                </div>
            `;
            document.body.appendChild(loadingDiv);

            document.body.appendChild(form);
            form.submit();
        } else {
            input.value = '';
        }
    }
}

// وظيفة تحديد/إلغاء تحديد الكل
function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('.official-checkbox');
    const toggleBtn = document.getElementById('toggleSelectAll');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');

    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(checkbox => {
        checkbox.checked = !allChecked;
    });

    if (!allChecked) {
        toggleBtn.textContent = '❌ إلغاء التحديد';
        toggleBtn.style.background = '#dc3545';
        bulkDeleteBtn.style.display = 'inline-block';
    } else {
        toggleBtn.textContent = '☑️ تحديد الكل';
        toggleBtn.style.background = '#6c757d';
        bulkDeleteBtn.style.display = 'none';
    }
}

// وظيفة الحذف الجماعي
function bulkDelete() {
    const checkedBoxes = document.querySelectorAll('.official-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('يرجى اختيار مسؤولين للحذف');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف ${checkedBoxes.length} مسؤول؟`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "organizations:bulk_delete_officials" %}';

        // إضافة CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                         document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
        }

        // إضافة IDs المحددة
        checkedBoxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'official_ids';
            input.value = checkbox.value;
            form.appendChild(input);
        });

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
