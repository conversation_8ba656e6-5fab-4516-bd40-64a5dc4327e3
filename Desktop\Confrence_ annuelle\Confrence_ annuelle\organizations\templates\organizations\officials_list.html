{% extends 'base.html' %}
{% load static %}
{% load i18n %}


{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .officials-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .officials-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .officials-title {
        color: #2c3e50;
        font-size: 2.5rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    .officials-subtitle {
        color: #7f8c8d;
        text-align: center;
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .search-filters {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .category-section {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        margin-bottom: 2rem;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .category-header {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        padding: 1.5rem 2rem;
        font-size: 1.4rem;
        font-weight: 600;
        text-align: center;
        position: relative;
    }

    .category-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.1) 75%);
        background-size: 20px 20px;
    }

    .officials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
        padding: 2rem;
    }

    .official-card {
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: 1px solid rgba(52, 152, 219, 0.2);
        position: relative;
        overflow: hidden;
    }

    .official-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
    }

    .official-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-header-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .card-actions {
        display: flex;
        gap: 0.5rem;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .official-card:hover .card-actions {
        opacity: 1;
    }

    .card-actions .btn {
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    .card-actions .btn:hover {
        transform: scale(1.1);
    }

    .card-footer-info {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }

    .official-checkbox {
        transform: scale(1.2);
    }

    .official-title {
        color: #2c3e50;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }

    .official-position {
        color: #7f8c8d;
        font-size: 0.95rem;
        margin-bottom: 1rem;
        font-style: italic;
    }

    .official-contact {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .contact-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #5a6c7d;
        font-size: 0.9rem;
    }

    .contact-item i {
        width: 16px;
        color: #3498db;
    }

    .stats-bar {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 1rem 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;

    }

    .stats-info {
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    .stats-item {
        display: flex;
        align-items: center;
        color: #2c3e50;
        font-weight: 600;
        white-space: nowrap;
    }

    .export-btn {
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .export-btn:hover {
        background: linear-gradient(135deg, #229954, #27ae60);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        color: white;
        text-decoration: none;
    }

    .action-buttons {
        display: flex !important;
        align-items: center;
        gap: 0.5rem;
        margin-left: auto;
        flex-shrink: 0;
        min-width: 300px;
    }

    .btn-success {
        background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
        border: none !important;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        color: white !important;
        text-decoration: none !important;
        display: inline-block !important;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #229954, #27ae60);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
    }

    .btn-info {
        background: linear-gradient(135deg, #3498db, #2980b9) !important;
        border: none !important;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        color: white !important;
    }

    .btn-info:hover {
        background: linear-gradient(135deg, #2980b9, #1f4e79);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    .btn-danger {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        background: linear-gradient(135deg, #c0392b, #a93226);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
    }

    .no-results {
        text-align: center;
        padding: 3rem;
        color: #7f8c8d;
        font-size: 1.1rem;
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #3498db, #2980b9);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #2980b9, #1f4e79);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    @media (max-width: 768px) {
        .officials-grid {
            grid-template-columns: 1fr;
            padding: 1rem;
        }

        .officials-title {
            font-size: 2rem;
        }

        .stats-bar {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .stats-info {
            flex-direction: column;
            gap: 0.5rem;
        }

        .action-buttons {
            justify-content: center;
            flex-wrap: wrap;
        }
    }
</style>
{% endblock %}

{% block content %}

<!-- اختبار الأزرار في أعلى الصفحة -->
<div style="position: fixed; top: 0; left: 0; right: 0; background: red; color: white; padding: 10px; z-index: 9999; text-align: center;">
    <h1>🚨 اختبار الأزرار - تحديث 17:39 🚨</h1>
    <a href="{% url 'organizations:add_official' %}" style="background: green; color: white; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px;">إضافة مسؤول</a>
    <a href="{% url 'organizations:export_officials' %}" style="background: blue; color: white; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px;">تصدير</a>
</div>

<div class="officials-container" style="margin-top: 100px;">
    <div class="container">
        <!-- Header -->
        <div class="officials-header">
            <h1 class="officials-title">
                <i class="fas fa-users-cog me-3"></i>
                {{ title }}
            </h1>
            <p class="officials-subtitle">
                قائمة شاملة بالمسؤولين الرسميين مصنفة حسب الوزارات والمؤسسات
            </p>
        </div>

        <!-- Action Buttons Test -->
        <div style="background: red; color: white; padding: 20px; margin: 20px 0; text-align: center; font-size: 24px; border: 5px solid yellow;">
            <h2>🔴 الأزرار هنا - تحديث 17:38 🔴</h2>
            <div class="btn-group" role="group">
                <a href="{% url 'organizations:add_official' %}" class="btn btn-success btn-lg" style="margin: 10px; padding: 15px 30px;">
                    <i class="fas fa-plus me-2"></i>إضافة مسؤول جديد
                </a>
                <a href="{% url 'organizations:export_officials' %}" class="btn btn-primary btn-lg" style="margin: 10px; padding: 15px 30px;">
                    <i class="fas fa-file-excel me-2"></i>تصدير إلى Excel
                </a>
                <a href="{% url 'organizations:download_officials_template' %}" class="btn btn-info btn-lg" style="margin: 10px; padding: 15px 30px;">
                    <i class="fas fa-download me-2"></i>تحميل قالب
                </a>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="search-filters">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search_query }}" placeholder="البحث في الأسماء والمناصب...">
                </div>
                <div class="col-md-3">
                    <label for="category" class="form-label">الفئة</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">جميع الفئات</option>
                        {% for key, value in category_choices %}
                            <option value="{{ key }}" {% if category_filter == key %}selected{% endif %}>
                                {{ value }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="title" class="form-label">اللقب</label>
                    <select class="form-select" id="title" name="title">
                        <option value="">جميع الألقاب</option>
                        {% for key, value in title_choices %}
                            <option value="{{ key }}" {% if title_filter == key %}selected{% endif %}>
                                {{ value }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Stats and Actions -->
        <div class="stats-bar">
            <div class="stats-info">
                <div class="stats-item">
                    <i class="fas fa-users me-2"></i>
                    إجمالي المسؤولين: {{ total_count }}
                </div>
                <div class="stats-item">
                    <i class="fas fa-layer-group me-2"></i>
                    الفئات: {{ categories_count }}
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{% url 'organizations:add_official' %}" class="btn btn-success me-2">
                    <i class="fas fa-plus me-1"></i>إضافة مسؤول
                </a>

                <div class="btn-group me-2" role="group">
                    <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-1"></i>استيراد/تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'organizations:export_officials' %}">
                            <i class="fas fa-file-excel me-2"></i>تصدير إلى Excel
                        </a></li>
                        <li><a class="dropdown-item" href="{% url 'organizations:download_officials_template' %}">
                            <i class="fas fa-download me-2"></i>تحميل قالب الاستيراد
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#importModal">
                            <i class="fas fa-upload me-2"></i>استيراد من Excel
                        </a></li>
                    </ul>
                </div>

                <button type="button" class="btn btn-danger" id="bulkDeleteBtn" style="display: none;">
                    <i class="fas fa-trash me-1"></i>حذف المحدد
                </button>
            </div>
        </div>

        <!-- Officials by Category -->
        {% if categories_with_officials %}
            {% for officials_data, category_name in categories_with_officials %}
                {% with officials=officials_data.0 officials_count=officials_data.1 %}
                <div class="category-section">
                    <div class="category-header">
                        <i class="fas fa-building me-3"></i>
                        {{ category_name }}
                        <span class="badge bg-light text-dark ms-3">{{ officials_count }}</span>
                    </div>
                    <div class="officials-grid">
                        {% for official in officials %}
                            <div class="official-card">
                                <div class="card-header-actions">
                                    <input type="checkbox" class="form-check-input official-checkbox"
                                           value="{{ official.id }}" style="display: none;">
                                    <div class="card-actions">
                                        <a href="{% url 'organizations:edit_official' official.id %}"
                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'organizations:delete_official' official.id %}"
                                           class="btn btn-sm btn-outline-danger" title="حذف"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا المسؤول؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="official-title">{{ official.get_full_title }}</div>
                                <div class="official-position">{{ official.position }}</div>
                                <div class="official-contact">
                                    {% if official.phone %}
                                        <div class="contact-item">
                                            <i class="fas fa-phone"></i>
                                            <span>{{ official.phone }}</span>
                                        </div>
                                    {% endif %}
                                    {% if official.email %}
                                        <div class="contact-item">
                                            <i class="fas fa-envelope"></i>
                                            <span>{{ official.email }}</span>
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="card-footer-info">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-plus me-1"></i>
                                        أضيف في: {{ official.created_at|date:"Y/m/d" }}
                                    </small>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
                {% endwith %}
            {% endfor %}
        {% else %}
            <div class="no-results">
                <i class="fas fa-search fa-3x mb-3"></i>
                <h4>لا توجد نتائج</h4>
                <p>لم يتم العثور على مسؤولين يطابقون معايير البحث المحددة.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد المسؤولين من Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'organizations:import_officials' %}" enctype="multipart/form-data">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="importFile" class="form-label">اختر ملف Excel</label>
                        <input type="file" class="form-control" id="importFile" name="file"
                               accept=".xlsx,.xls" required>
                        <div class="form-text">
                            يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد:</h6>
                        <ul class="mb-0">
                            <li>تأكد من أن الملف يحتوي على الأعمدة المطلوبة</li>
                            <li>الصف الأول يجب أن يحتوي على عناوين الأعمدة</li>
                            <li>يمكنك تحميل قالب الاستيراد للحصول على التنسيق الصحيح</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>استيراد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.official-checkbox');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const selectAllBtn = document.getElementById('selectAllBtn');

    // إظهار/إخفاء أزرار الحذف الجماعي
    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.official-checkbox:checked');
        if (checkedBoxes.length > 0) {
            bulkDeleteBtn.style.display = 'inline-block';
        } else {
            bulkDeleteBtn.style.display = 'none';
        }
    }

    // إضافة مستمع للأحداث لكل checkbox
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    // الحذف الجماعي
    bulkDeleteBtn.addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.official-checkbox:checked');
        if (checkedBoxes.length === 0) {
            alert('يرجى اختيار مسؤولين للحذف');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف ${checkedBoxes.length} مسؤول؟`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{% url "organizations:bulk_delete_officials" %}';

            // إضافة CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);

            // إضافة IDs المحددة
            checkedBoxes.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'official_ids';
                input.value = checkbox.value;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
        }
    });

    // إضافة زر تحديد الكل (اختياري)
    const statsBar = document.querySelector('.stats-bar');
    if (statsBar && checkboxes.length > 0) {
        const selectAllContainer = document.createElement('div');
        selectAllContainer.className = 'select-all-container';
        selectAllContainer.innerHTML = `
            <button type="button" class="btn btn-sm btn-outline-secondary" id="toggleSelectAll">
                <i class="fas fa-check-square me-1"></i>تحديد الكل
            </button>
        `;

        const actionButtons = statsBar.querySelector('.action-buttons');
        if (actionButtons) {
            actionButtons.insertBefore(selectAllContainer, actionButtons.firstChild);
        }

        // وظيفة تحديد/إلغاء تحديد الكل
        const toggleSelectAllBtn = document.getElementById('toggleSelectAll');
        let allSelected = false;

        toggleSelectAllBtn.addEventListener('click', function() {
            allSelected = !allSelected;
            checkboxes.forEach(checkbox => {
                checkbox.checked = allSelected;
                checkbox.style.display = allSelected ? 'inline-block' : 'none';
            });

            if (allSelected) {
                toggleSelectAllBtn.innerHTML = '<i class="fas fa-square me-1"></i>إلغاء التحديد';
                toggleSelectAllBtn.className = 'btn btn-sm btn-warning';
            } else {
                toggleSelectAllBtn.innerHTML = '<i class="fas fa-check-square me-1"></i>تحديد الكل';
                toggleSelectAllBtn.className = 'btn btn-sm btn-outline-secondary';
            }

            updateBulkActions();
        });
    }
});
</script>
{% endblock %}
