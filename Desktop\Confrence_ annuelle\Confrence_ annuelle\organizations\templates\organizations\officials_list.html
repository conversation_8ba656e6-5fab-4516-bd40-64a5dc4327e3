{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    /* تصميم عام للصفحة */
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    /* قسم الإحصائيات */
    .stats-section {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        transition: transform 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.3);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 800;
        display: block;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
    }

    .stat-label {
        font-size: 1rem;
        font-weight: 500;
        opacity: 0.9;
    }

    .stat-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        opacity: 0.8;
    }

    /* قسم الفلترة */
    .filter-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .filter-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    /* بطاقات المسؤولين */
    .category-section {
        margin-bottom: 3rem;
    }

    .category-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem 2rem;
        border-radius: 15px 15px 0 0;
        font-size: 1.4rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .category-title {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .category-count {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .officials-grid {
        background: rgba(255, 255, 255, 0.95);
        padding: 2rem;
        border-radius: 0 0 15px 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    .official-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .official-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2, #4facfe);
    }

    .official-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        border-color: #667eea;
    }

    .official-photo {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #667eea;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .official-photo-placeholder {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .official-title {
        color: #667eea;
        font-weight: 700;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .official-name {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        line-height: 1.3;
    }

    .official-position {
        color: #7f8c8d;
        font-size: 1rem;
        margin-bottom: 1rem;
        font-style: italic;
        line-height: 1.4;
    }

    .official-contact {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .contact-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #5a6c7d;
        font-size: 0.9rem;
    }

    .contact-item i {
        width: 16px;
        color: #667eea;
    }

    .card-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }

    .btn-action {
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        text-decoration: none;
    }

    .card-checkbox {
        position: absolute;
        top: 1rem;
        right: 1rem;
        transform: scale(1.3);
    }

    .card-date {
        position: absolute;
        bottom: 1rem;
        left: 1rem;
        font-size: 0.8rem;
        color: #95a5a6;
        display: flex;
        align-items: center;
        gap: 0.3rem;
    }

    /* تحسينات إضافية */
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
        padding: 0.75rem 1rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn {
        border-radius: 10px;
        font-weight: 500;
        transition: all 0.3s ease;
        padding: 0.75rem 1.5rem;
    }

    .btn:hover {
        transform: translateY(-2px);
    }

    /* تجاوبية */
    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }

        .stat-number {
            font-size: 2rem;
        }

        .officials-grid {
            padding: 1rem;
        }

        .official-card {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
{% csrf_token %}

<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="fas fa-users-cog me-3"></i>
                    {{ title }}
                </h1>
                <p class="page-subtitle">
                    إدارة شاملة للمسؤولين الرسميين في الحكومة الموريتانية
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex flex-wrap gap-2 justify-content-end">
                    <a href="{% url 'organizations:add_official' %}" class="btn btn-light btn-lg">
                        <i class="fas fa-plus me-2"></i>إضافة مسؤول
                    </a>

                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-light dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-2"></i>تصدير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'organizations:export_officials' %}">
                                <i class="fas fa-file-excel text-success me-2"></i>تصدير Excel
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'organizations:download_officials_template' %}">
                                <i class="fas fa-download text-info me-2"></i>تحميل قالب
                            </a></li>
                        </ul>
                    </div>

                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-light dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-upload me-2"></i>استيراد
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="document.getElementById('importFileInput').click()">
                                <i class="fas fa-file-excel text-warning me-2"></i>استيراد Excel
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">

            <!-- Statistics -->
            <div class="stats-section">
                <div class="row g-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <span class="stat-number">{{ total_count }}</span>
                            <div class="stat-label">إجمالي المسؤولين</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <span class="stat-number">{{ categories_count }}</span>
                            <div class="stat-label">الوزارات والمؤسسات</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-check-square"></i>
                            </div>
                            <span class="stat-number" id="selected-count">0</span>
                            <div class="stat-label">المحدد للحذف</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-trash-alt"></i>
                            </div>
                            <button type="button" id="bulkDeleteBtn" onclick="bulkDelete()"
                                    class="btn btn-outline-light btn-sm" style="display: none;">
                                <i class="fas fa-trash me-1"></i>حذف المحدد
                            </button>
                            <div class="stat-label" id="bulk-action-label">عمليات جماعية</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- مدخل ملف مخفي للاستيراد -->
            <input type="file" id="importFileInput" accept=".xlsx,.xls" style="display: none;" onchange="handleFileImport(this)">

            <!-- Filters -->
            <div class="filter-section">
                <div class="filter-title">
                    <i class="fas fa-filter"></i>
                    البحث والفلترة المتقدمة
                </div>
                <form method="get" class="row g-3">
                    <div class="col-lg-4 col-md-6">
                        <label class="form-label fw-bold">
                            <i class="fas fa-search me-1"></i>البحث العام
                        </label>
                        <input type="text" class="form-control" name="search" value="{{ search_query }}"
                               placeholder="ابحث في الأسماء، المناصب، أو معلومات الاتصال...">
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label class="form-label fw-bold">
                            <i class="fas fa-building me-1"></i>الوزارة/المؤسسة
                        </label>
                        <select class="form-select" name="category">
                            <option value="">جميع الوزارات والمؤسسات</option>
                            {% for key, value in category_choices %}
                                <option value="{{ key }}" {% if category_filter == key %}selected{% endif %}>
                                    {{ value }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-6">
                        <label class="form-label fw-bold">
                            <i class="fas fa-user-tie me-1"></i>اللقب الرسمي
                        </label>
                        <select class="form-select" name="title">
                            <option value="">جميع الألقاب</option>
                            {% for key, value in title_choices %}
                                <option value="{{ key }}" {% if title_filter == key %}selected{% endif %}>
                                    {{ value }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-6">
                        <label class="form-label fw-bold">
                            <i class="fas fa-sort me-1"></i>ترتيب النتائج
                        </label>
                        <select class="form-select" name="sort">
                            <option value="name" {% if current_sort == 'name' %}selected{% endif %}>الاسم</option>
                            <option value="title" {% if current_sort == 'title' %}selected{% endif %}>اللقب</option>
                            <option value="category" {% if current_sort == 'category' %}selected{% endif %}>الوزارة</option>
                            <option value="date" {% if current_sort == 'date' %}selected{% endif %}>تاريخ الإضافة</option>
                        </select>
                    </div>
                    <div class="col-lg-1 col-md-6">
                        <label class="form-label fw-bold">
                            <i class="fas fa-play me-1"></i>تطبيق
                        </label>
                        <button type="submit" class="btn btn-primary d-block w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>

                <div class="row mt-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex gap-2">
                                <button type="button" id="selectAllBtn" onclick="toggleSelectAll()"
                                        class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-check-square me-1"></i>تحديد الكل
                                </button>
                                <button type="button" onclick="clearFilters()" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-times me-1"></i>مسح الفلاتر
                                </button>
                            </div>
                            <div class="text-muted">
                                <small>
                                    <i class="fas fa-info-circle me-1"></i>
                                    عرض {{ total_count }} مسؤول من {{ categories_count }} وزارة/مؤسسة
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Officials List -->
            {% if categories_with_officials %}
                {% for officials_data, category_name in categories_with_officials %}
                    {% with officials=officials_data.0 officials_count=officials_data.1 %}
                    <div class="category-section">
                        <div class="category-header">
                            <div class="category-title">
                                <i class="fas fa-building"></i>
                                {{ category_name }}
                            </div>
                            <div class="category-count">{{ officials_count }} مسؤول</div>
                        </div>

                        <div class="officials-grid">
                            <div class="row g-4">
                                {% for official in officials %}
                                <div class="col-xl-4 col-lg-6 col-md-12">
                                    <div class="official-card">
                                        <input type="checkbox" class="form-check-input official-checkbox card-checkbox"
                                               name="selected_officials" value="{{ official.id }}"
                                               id="official-{{ official.id }}">

                                        <div class="d-flex align-items-start mb-3">
                                            <div class="flex-shrink-0 me-3">
                                                {% if official.photo %}
                                                    <img src="{{ official.photo.url }}" alt="{{ official.get_full_title }}" class="official-photo">
                                                {% else %}
                                                    <div class="official-photo-placeholder">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                {% endif %}
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="official-title">{{ official.get_title_display }}</div>
                                                <div class="official-name">{{ official.name }}</div>
                                                <div class="official-position">{{ official.position }}</div>
                                            </div>
                                        </div>

                                        {% if official.phone or official.email %}
                                            <div class="official-contact">
                                                {% if official.phone %}
                                                    <div class="contact-item">
                                                        <i class="fas fa-phone"></i>
                                                        <span>{{ official.phone }}</span>
                                                    </div>
                                                {% endif %}
                                                {% if official.email %}
                                                    <div class="contact-item">
                                                        <i class="fas fa-envelope"></i>
                                                        <span>{{ official.email }}</span>
                                                    </div>
                                                {% endif %}
                                            </div>
                                        {% endif %}

                                        <div class="card-actions">
                                            <a href="{% url 'organizations:edit_official' official.pk %}"
                                               class="btn btn-outline-primary btn-action">
                                                <i class="fas fa-edit"></i>تعديل
                                            </a>
                                            <a href="{% url 'organizations:delete_official' official.pk %}"
                                               class="btn btn-outline-danger btn-action"
                                               onclick="return confirm('هل أنت متأكد من حذف هذا المسؤول؟')">
                                                <i class="fas fa-trash"></i>حذف
                                            </a>
                                        </div>

                                        <div class="card-date">
                                            <i class="fas fa-calendar-plus"></i>
                                            {{ official.created_at|date:"Y/m/d" }}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endwith %}
                {% endfor %}
            {% else %}
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-search" style="font-size: 4rem; color: #bdc3c7;"></i>
                    </div>
                    <h3 class="text-muted mb-3">لا توجد نتائج</h3>
                    <p class="text-muted mb-4">لم يتم العثور على مسؤولين يطابقون معايير البحث المحددة.</p>
                    <a href="{% url 'organizations:officials_list' %}" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>عرض جميع المسؤولين
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد المسؤولين من Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'organizations:import_officials' %}" enctype="multipart/form-data">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="importFile" class="form-label">اختر ملف Excel</label>
                        <input type="file" class="form-control" id="importFile" name="file"
                               accept=".xlsx,.xls" required>
                        <div class="form-text">
                            يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد:</h6>
                        <ul class="mb-0">
                            <li>تأكد من أن الملف يحتوي على الأعمدة المطلوبة</li>
                            <li>الصف الأول يجب أن يحتوي على عناوين الأعمدة</li>
                            <li>يمكنك تحميل قالب الاستيراد للحصول على التنسيق الصحيح</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>استيراد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استيراد المسؤولين من Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'organizations:import_officials' %}" enctype="multipart/form-data">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="importFile" class="form-label">اختر ملف Excel</label>
                        <input type="file" class="form-control" id="importFile" name="file"
                               accept=".xlsx,.xls" required>
                        <div class="form-text">
                            يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد:</h6>
                        <ul class="mb-0">
                            <li>تأكد من أن الملف يحتوي على الأعمدة المطلوبة</li>
                            <li>الصف الأول يجب أن يحتوي على عناوين الأعمدة</li>
                            <li>يمكنك تحميل قالب الاستيراد للحصول على التنسيق الصحيح</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>استيراد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.official-checkbox');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const selectedCountSpan = document.getElementById('selected-count');

    // إظهار/إخفاء أزرار الحذف الجماعي
    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.official-checkbox:checked');
        const count = checkedBoxes.length;

        if (selectedCountSpan) {
            selectedCountSpan.textContent = count;
        }

        if (count > 0) {
            bulkDeleteBtn.style.display = 'inline-block';
        } else {
            bulkDeleteBtn.style.display = 'none';
        }

        updateSelectAllButton();
    }

    // تحديث زر تحديد الكل
    function updateSelectAllButton() {
        const checkedCount = document.querySelectorAll('.official-checkbox:checked').length;
        const totalCount = checkboxes.length;

        if (selectAllBtn) {
            if (checkedCount === 0) {
                selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i>';
                selectAllBtn.className = 'btn btn-outline-secondary btn-sm';
                selectAllBtn.title = 'تحديد الكل';
            } else if (checkedCount === totalCount) {
                selectAllBtn.innerHTML = '<i class="fas fa-square"></i>';
                selectAllBtn.className = 'btn btn-warning btn-sm';
                selectAllBtn.title = 'إلغاء التحديد';
            } else {
                selectAllBtn.innerHTML = '<i class="fas fa-minus-square"></i>';
                selectAllBtn.className = 'btn btn-info btn-sm';
                selectAllBtn.title = `محدد ${checkedCount} من ${totalCount}`;
            }
        }
    }

    // إضافة مستمع للأحداث لكل checkbox
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    // تحديث الحالة الأولية
    updateBulkActions();

});

// وظيفة تحديد/إلغاء تحديد الكل
function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('.official-checkbox');
    const checkedCount = document.querySelectorAll('.official-checkbox:checked').length;
    const shouldCheck = checkedCount === 0;

    checkboxes.forEach(checkbox => {
        checkbox.checked = shouldCheck;
    });

    // تحديث العدادات والأزرار
    const event = new Event('change');
    if (checkboxes.length > 0) {
        checkboxes[0].dispatchEvent(event);
    }
}

// وظيفة تحديد/إلغاء تحديد الكل
function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('.official-checkbox');
    const checkedCount = document.querySelectorAll('.official-checkbox:checked').length;
    const shouldCheck = checkedCount === 0;

    checkboxes.forEach(checkbox => {
        checkbox.checked = shouldCheck;
    });

    // تحديث العدادات والأزرار
    const event = new Event('change');
    if (checkboxes.length > 0) {
        checkboxes[0].dispatchEvent(event);
    }
}

// وظيفة الحذف الجماعي
function bulkDelete() {
    const checkedBoxes = document.querySelectorAll('.official-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('يرجى اختيار مسؤولين للحذف');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف ${checkedBoxes.length} مسؤول؟`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "organizations:bulk_delete_officials" %}';

        // إضافة CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                         document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         '{{ csrf_token }}';
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
        }

        // إضافة IDs المحددة
        checkedBoxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'official_ids';
            input.value = checkbox.value;
            form.appendChild(input);
        });

        document.body.appendChild(form);
        form.submit();
    }
}

// وظيفة التعامل مع استيراد الملفات
function handleFileImport(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];
        const fileName = file.name;
        const fileSize = (file.size / 1024 / 1024).toFixed(2);

        // التحقق من نوع الملف
        if (!fileName.match(/\.(xlsx|xls)$/i)) {
            alert('يرجى اختيار ملف Excel صالح (.xlsx أو .xls)');
            input.value = '';
            return;
        }

        // التحقق من حجم الملف
        if (file.size > 10 * 1024 * 1024) {
            alert('حجم الملف كبير جداً. يرجى اختيار ملف أقل من 10 ميجابايت');
            input.value = '';
            return;
        }

        // تأكيد الاستيراد
        if (confirm(`هل تريد استيراد الملف: ${fileName} (${fileSize} MB)؟`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{% url "organizations:import_officials" %}';
            form.enctype = 'multipart/form-data';

            // إضافة CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                             '{{ csrf_token }}';
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrfmiddlewaretoken';
                csrfInput.value = csrfToken;
                form.appendChild(csrfInput);
            }

            // إضافة الملف
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.name = 'file';
            fileInput.files = input.files;
            form.appendChild(fileInput);

            document.body.appendChild(form);
            form.submit();
        } else {
            input.value = '';
        }
    }
}

// دالة مسح الفلاتر
function clearFilters() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input[type="text"], select');
    inputs.forEach(input => {
        if (input.type === 'text') {
            input.value = '';
        } else if (input.tagName === 'SELECT') {
            input.selectedIndex = 0;
        }
    });
    form.submit();
}

// تحديث عرض زر الحذف الجماعي
function updateBulkDeleteDisplay() {
    const selectedCount = document.querySelectorAll('.official-checkbox:checked').length;
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const bulkActionLabel = document.getElementById('bulk-action-label');

    if (selectedCount > 0) {
        bulkDeleteBtn.style.display = 'inline-block';
        bulkActionLabel.style.display = 'none';
        bulkDeleteBtn.innerHTML = `<i class="fas fa-trash me-1"></i>حذف ${selectedCount}`;
    } else {
        bulkDeleteBtn.style.display = 'none';
        bulkActionLabel.style.display = 'block';
    }
}

// تحديث الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مستمع للأحداث لجميع checkboxes
    document.querySelectorAll('.official-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkDeleteDisplay();

            // تحديث العداد
            const selectedCount = document.querySelectorAll('.official-checkbox:checked').length;
            document.getElementById('selected-count').textContent = selectedCount;
        });
    });
});
</script>
{% endblock %}
