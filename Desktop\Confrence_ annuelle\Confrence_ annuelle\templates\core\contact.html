{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "اتصل بنا - المؤتمر السنوي للسيرة النبوية" %}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        --glass-bg: rgba(255, 255, 255, 0.25);
        --glass-border: rgba(255, 255, 255, 0.18);
        --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
        --shadow-heavy: 0 15px 35px rgba(31, 38, 135, 0.5);
        --border-radius: 20px;
        --transition: all 0.4s cubic-bezier(0.23, 1, 0.320, 1);
    }

    * {
        font-family: 'Poppins', sans-serif;
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    /* Hero section - تصميم جديد كلياً */
    .hero-contact {
        background: var(--primary-gradient);
        position: relative;
        overflow: hidden;
        padding: 8rem 0 4rem;
        margin-bottom: 0;
    }

    .hero-contact::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
        animation: float 20s ease-in-out infinite;
    }

    .hero-contact::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 100px;
        background: linear-gradient(to top, #f5f7fa, transparent);
        clip-path: polygon(0 100%, 100% 100%, 100% 0, 0 80%);
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(2deg); }
    }
    
    .hero-title {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .hero-subtitle {
        font-size: 1.3rem;
        font-weight: 300;
        opacity: 0.9;
        margin-bottom: 2rem;
        position: relative;
        z-index: 2;
    }

    .hero-icon {
        font-size: 4rem;
        margin-bottom: 2rem;
        opacity: 0.8;
        position: relative;
        z-index: 2;
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }
    
    /* بطاقات جديدة بتصميم Glass Morphism */
    .glass-card {
        background: var(--glass-bg);
        backdrop-filter: blur(16px);
        -webkit-backdrop-filter: blur(16px);
        border-radius: var(--border-radius);
        border: 1px solid var(--glass-border);
        box-shadow: var(--shadow-light);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .glass-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    }

    .glass-card:hover {
        transform: translateY(-10px);
        box-shadow: var(--shadow-heavy);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .contact-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    /* عناصر المعلومات الجديدة */
    .info-item {
        padding: 2rem;
        text-align: center;
        position: relative;
        transition: var(--transition);
    }

    .info-item:hover {
        transform: scale(1.05);
    }

    .info-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .info-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: all 0.6s ease;
    }

    .info-item:hover .info-icon::before {
        left: 100%;
    }

    .info-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }

    .info-text {
        color: #6c757d;
        line-height: 1.6;
        margin: 0;
    }

    .info-text a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
        transition: var(--transition);
    }

    .info-text a:hover {
        color: #764ba2;
        text-decoration: underline;
    }
    
    /* تصميم النموذج الجديد */
    .contact-form {
        background: var(--glass-bg);
        backdrop-filter: blur(16px);
        border-radius: var(--border-radius);
        border: 1px solid var(--glass-border);
        padding: 3rem;
        box-shadow: var(--shadow-light);
    }

    .form-floating {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        font-size: 1rem;
        transition: var(--transition);
        backdrop-filter: blur(8px);
    }

    .form-control:focus {
        background: rgba(255, 255, 255, 0.95);
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        transform: translateY(-2px);
        outline: none;
    }

    .form-control::placeholder {
        color: rgba(108, 117, 125, 0.7);
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    /* أزرار جديدة بتصميم متطور */
    .btn-modern {
        background: var(--primary-gradient);
        border: none;
        border-radius: 50px;
        padding: 15px 40px;
        font-weight: 600;
        font-size: 1.1rem;
        color: white;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: all 0.6s ease;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-modern:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-modern:active {
        transform: translateY(-1px) scale(1.02);
    }
    
    .input-group-text {
        background-color: #f8f9fa;
        border-width: 2px;
        border-color: #ced4da;
        color: #6c757d;
    }
    
    /* Contact info styles */
    .contact-info-card {
        border: none;
        border-radius: 1.5rem;
        overflow: hidden;
        box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.1);
        transition: transform 0.4s ease, box-shadow 0.4s ease;
        height: 100%;
    }
    
    .contact-info-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.15);
    }
    
    .contact-info-item {
        text-align: center;
        transition: all 0.3s ease;
        padding: 1.5rem;
        border-radius: 1rem;
    }
    
    .contact-info-item:hover {
        background-color: rgba(57, 73, 171, 0.05);
    }
    
    .contact-info-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 70px;
        height: 70px;
        margin: 0 auto;
        transition: all 0.4s ease;
        background-color: rgba(57, 73, 171, 0.1) !important;
        color: #3949ab !important;
    }
    
    .contact-info-item:hover .contact-info-icon {
        background-color: #3949ab !important;
        color: white !important;
        transform: scale(1.1) rotate(10deg);
        box-shadow: 0 0.5rem 1rem rgba(57, 73, 171, 0.3);
    }
    
    .contact-info-title {
        font-weight: 700;
        margin: 1rem 0 0.5rem;
        text-align: center;
        color: #3949ab;
    }
    
    .contact-info-text {
        text-align: center;
        color: #6c757d;
        font-size: 1.1rem;
    }
    
    .social-icons .btn {
        width: 45px;
        height: 45px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.4s ease;
        border-radius: 50%;
        margin: 0 0.5rem;
        background-color: rgba(57, 73, 171, 0.1);
        color: #3949ab;
        border: none;
    }
    
    /* تحسين أزرار وسائل التواصل */
    .social-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        text-decoration: none;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        font-size: 1.2rem;
    }

    .social-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        transform: scale(0);
        transition: all 0.3s ease;
        z-index: -1;
    }

    .social-btn:hover::before {
        transform: scale(1);
    }

    .social-btn.twitter {
        background: rgba(29, 161, 242, 0.1);
        color: #1da1f2;
        border: 2px solid rgba(29, 161, 242, 0.2);
    }

    .social-btn.twitter::before {
        background: #1da1f2;
    }

    .social-btn.twitter:hover {
        color: white;
        transform: translateY(-5px) scale(1.1);
        box-shadow: 0 10px 25px rgba(29, 161, 242, 0.3);
    }

    .social-btn.facebook {
        background: rgba(24, 119, 242, 0.1);
        color: #1877f2;
        border: 2px solid rgba(24, 119, 242, 0.2);
    }

    .social-btn.facebook::before {
        background: #1877f2;
    }

    .social-btn.facebook:hover {
        color: white;
        transform: translateY(-5px) scale(1.1);
        box-shadow: 0 10px 25px rgba(24, 119, 242, 0.3);
    }

    .social-btn.instagram {
        background: rgba(225, 48, 108, 0.1);
        color: #e1306c;
        border: 2px solid rgba(225, 48, 108, 0.2);
    }

    .social-btn.instagram::before {
        background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    }

    .social-btn.instagram:hover {
        color: white;
        transform: translateY(-5px) scale(1.1);
        box-shadow: 0 10px 25px rgba(225, 48, 108, 0.3);
    }

    /* تحسين الأسئلة الشائعة */
    .accordion-item {
        border: none;
        border-radius: 15px !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .accordion-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
    }

    .accordion-button {
        background: white;
        border: none;
        padding: 20px 25px;
        font-weight: 600;
        font-size: 1.1rem;
        border-radius: 15px !important;
        transition: all 0.3s ease;
    }

    .accordion-button:not(.collapsed) {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: none;
    }

    .accordion-button:focus {
        box-shadow: none;
        border: none;
    }

    .accordion-button::after {
        transition: all 0.3s ease;
    }

    .accordion-body {
        padding: 25px;
        background: rgba(102, 126, 234, 0.02);
        font-size: 1.05rem;
        line-height: 1.7;
        color: #555;
    }

    .section-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    /* Map styles */
    #contact-map {
        height: 350px;
        width: 100%;
        border-radius: 12px;
        box-shadow: 0 0.4rem 0.8rem rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        border: 8px solid white;
        transition: all 0.4s ease;
    }
    
    #contact-map:hover {
        box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.15);
        transform: translateY(-5px);
    }
    
    /* Animation styles */
    .fade-in {
        animation: fadeIn 1s ease-in-out;
    }
    
    .slide-up {
        animation: slideUp 0.8s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes slideUp {
        from { transform: translateY(30px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }
    
    /* Button styles */
    .btn-primary {
        background: linear-gradient(135deg, #3949ab 0%, #1a237e 100%);
        border: none;
        border-radius: 0.75rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.4s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.8rem 1.5rem rgba(0, 0, 0, 0.2);
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
    }
    
    /* Section styles */
    .section-title {
        position: relative;
        display: inline-block;
        margin-bottom: 1.5rem;
        font-weight: 700;
        color: #3949ab;
    }
    
    .section-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 50px;
        height: 3px;
        background: linear-gradient(135deg, #3949ab 0%, #1a237e 100%);
        border-radius: 3px;
    }
    
    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .hero-mini {
            padding: 3rem 0;
        }
        
        .contact-info-icon {
            width: 70px;
            height: 70px;
        }
        
        #contact-map {
            height: 300px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section الجديد -->
<section class="hero-contact">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <div class="hero-icon">
                    <i class="fas fa-envelope-open"></i>
                </div>
                <h1 class="hero-title">{% trans "تواصل معنا" %}</h1>
                <p class="hero-subtitle">
                    {% trans "نحن هنا للإجابة على جميع استفساراتكم حول المؤتمر السنوي للسيرة النبوية" %}
                </p>
                <div class="hero-stats">
                    <div class="row g-4 justify-content-center">
                        <div class="col-md-3 col-6">
                            <div class="stat-item">
                                <div class="stat-number">24/7</div>
                                <div class="stat-label">{% trans "دعم متواصل" %}</div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="stat-item">
                                <div class="stat-number">< 24h</div>
                                <div class="stat-label">{% trans "وقت الاستجابة" %}</div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="stat-item">
                                <div class="stat-number">50+</div>
                                <div class="stat-label">{% trans "دولة" %}</div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="stat-item">
                                <div class="stat-number">15+</div>
                                <div class="stat-label">{% trans "دورة" %}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-4 fade-in">
                <h2 class="section-title">{% trans "موقعنا" %}</h2>
                <p class="text-muted">{% trans "يمكنك زيارتنا في المكان التالي" %}</p>
            </div>
            <div class="col-12 slide-up">
                <div id="contact-map"></div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="py-5">
    <div class="container">
        <div class="row g-5">
            <!-- Contact Information -->
            <div class="col-lg-5 slide-up" style="animation-delay: 0.2s;">
                <div class="card contact-info-card">
                    <div class="card-header bg-primary text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i>{% trans "معلومات الاتصال" %}</h3>
                    </div>
                    <div class="card-body p-4">
                        <div class="contact-info-item mb-4">
                            <div class="contact-info-icon rounded-circle p-3 mb-3" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
                                <i class="fas fa-map-marker-alt fa-2x text-white"></i>
                            </div>
                            <h5 class="contact-info-title">{% trans "العنوان" %}</h5>
                            <p class="contact-info-text">
                                <strong>{% trans "التجمع الثقافي الإسلامي" %}</strong><br>
                                نواكشوط - موريتانيا<br>
                                <small class="text-muted">{% trans "المقر الرئيسي للمؤتمر" %}</small>
                            </p>
                        </div>

                        <div class="contact-info-item mb-4">
                            <div class="contact-info-icon rounded-circle p-3 mb-3" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);">
                                <i class="fas fa-envelope fa-2x text-white"></i>
                            </div>
                            <h5 class="contact-info-title">{% trans "البريد الإلكتروني" %}</h5>
                            <p class="contact-info-text">
                                <a href="mailto:<EMAIL>" class="text-decoration-none text-primary">
                                    <strong><EMAIL></strong>
                                </a><br>
                                <small class="text-muted">{% trans "للاستفسارات العامة" %}</small>
                            </p>
                        </div>

                        <div class="contact-info-item mb-4">
                            <div class="contact-info-icon rounded-circle p-3 mb-3" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
                                <i class="fas fa-phone fa-2x text-white"></i>
                            </div>
                            <h5 class="contact-info-title">{% trans "الهاتف" %}</h5>
                            <p class="contact-info-text">
                                <a href="tel:+22232816779" class="text-decoration-none text-primary">
                                    <strong>+222 32 81 67 79</strong>
                                </a><br>
                                <small class="text-muted">{% trans "خط مباشر للمؤتمر" %}</small>
                            </p>
                        </div>

                        <div class="contact-info-item mb-4">
                            <div class="contact-info-icon rounded-circle p-3 mb-3" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                                <i class="fas fa-clock fa-2x text-white"></i>
                            </div>
                            <h5 class="contact-info-title">{% trans "ساعات العمل" %}</h5>
                            <p class="contact-info-text">
                                <strong>{% trans "الأحد - الخميس" %}</strong><br>
                                8:00 صباحاً - 4:00 مساءً<br>
                                <small class="text-muted">{% trans "بتوقيت نواكشوط" %}</small>
                            </p>
                        </div>

                        <div class="contact-info-item">
                            <div class="contact-info-icon rounded-circle p-3 mb-3" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);">
                                <i class="fas fa-calendar-alt fa-2x text-white"></i>
                            </div>
                            <h5 class="contact-info-title">{% trans "موعد المؤتمر القادم" %}</h5>
                            <p class="contact-info-text">
                                <strong>{% trans "ربيع الأول 1446 هـ" %}</strong><br>
                                <small class="text-muted">{% trans "سبتمبر 2024 م" %}</small>
                            </p>
                        </div>
                        
                        <div class="text-center mt-5">
                            <h5 class="mb-4 fw-bold text-primary">{% trans "تابعنا على وسائل التواصل" %}</h5>
                            <div class="social-icons">
                                <a href="#" class="social-btn twitter me-3" data-bs-toggle="tooltip" title="{% trans "تويتر" %}">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="social-btn facebook me-3" data-bs-toggle="tooltip" title="{% trans "فيسبوك" %}">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" class="social-btn instagram me-3" data-bs-toggle="tooltip" title="{% trans "انستغرام" %}">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                <a href="#" class="btn" data-bs-toggle="tooltip" title="{% trans "يوتيوب" %}">
                                    <i class="fab fa-youtube fa-lg"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Form -->
            <div class="col-lg-6 slide-up">
                <div class="card contact-form-card">
                    <div class="card-header bg-primary text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-paper-plane me-2"></i>{% trans "أرسل لنا رسالة" %}</h3>
                    </div>
                    <div class="card-body p-4">
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                        
                        <form method="post" class="needs-validation contact-form" novalidate>
                            {% csrf_token %}
                            <div class="mb-4">
                                <label for="name" class="form-label">{% trans "الاسم الكامل" %} <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                                <div class="invalid-feedback">
                                    {% trans "يرجى إدخال اسمك الكامل" %}
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="email" class="form-label">{% trans "البريد الإلكتروني" %} <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <div class="invalid-feedback">
                                    {% trans "يرجى إدخال بريد إلكتروني صحيح" %}
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="subject" class="form-label">{% trans "الموضوع" %} <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-heading"></i></span>
                                    <input type="text" class="form-control" id="subject" name="subject" required>
                                </div>
                                <div class="invalid-feedback">
                                    {% trans "يرجى إدخال موضوع الرسالة" %}
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="message" class="form-label">{% trans "الرسالة" %} <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-comment-alt"></i></span>
                                    <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
                                </div>
                                <div class="invalid-feedback">
                                    {% trans "يرجى كتابة رسالتك" %}
                                </div>
                            </div>
                            
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="fas fa-paper-plane me-2"></i> {% trans "إرسال" %}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم الأسئلة الشائعة -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5 fade-in">
                <h2 class="section-title">
                    <i class="fas fa-question-circle me-3 text-primary"></i>
                    {% trans "الأسئلة الشائعة" %}
                </h2>
                <p class="text-muted">{% trans "إجابات على أكثر الأسئلة شيوعاً حول المؤتمر" %}</p>
            </div>
            <div class="col-lg-10 mx-auto">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item mb-3">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                <i class="fas fa-calendar-alt me-2 text-primary"></i>
                                {% trans "متى يُعقد المؤتمر السنوي؟" %}
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {% trans "يُعقد المؤتمر السنوي للسيرة النبوية في شهر ربيع الأول من كل عام هجري، ويستمر لمدة ثلاثة أيام في نواكشوط، موريتانيا." %}
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item mb-3">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                <i class="fas fa-users me-2 text-success"></i>
                                {% trans "من يمكنه المشاركة في المؤتمر؟" %}
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {% trans "المؤتمر مفتوح للعلماء والدعاة والأئمة والباحثين في الدراسات الإسلامية، بالإضافة إلى ممثلي المؤسسات الدينية والثقافية من جميع أنحاء العالم." %}
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item mb-3">
                        <h2 class="accordion-header" id="faq3">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                <i class="fas fa-file-alt me-2 text-warning"></i>
                                {% trans "كيف يمكنني تقديم بحث للمؤتمر؟" %}
                            </button>
                        </h2>
                        <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {% trans "يمكن تقديم البحوث من خلال إرسال ملخص البحث والسيرة الذاتية للباحث عبر البريد الإلكتروني قبل الموعد المحدد. يجب أن يكون البحث متعلقاً بموضوع المؤتمر." %}
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item mb-3">
                        <h2 class="accordion-header" id="faq4">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                                <i class="fas fa-plane me-2 text-info"></i>
                                {% trans "هل يتم توفير الإقامة والسفر؟" %}
                            </button>
                        </h2>
                        <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {% trans "يتم توفير الإقامة والوجبات للمشاركين المدعوين رسمياً. أما بالنسبة للسفر، فيتم تقديم المساعدة حسب الإمكانيات المتاحة وأولوية المشاركة." %}
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq5">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5">
                                <i class="fas fa-language me-2 text-danger"></i>
                                {% trans "ما هي لغات المؤتمر؟" %}
                            </button>
                        </h2>
                        <div id="collapse5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {% trans "اللغة الرسمية للمؤتمر هي العربية، مع توفير الترجمة الفورية للغات الفرنسية والإنجليزية حسب الحاجة." %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثيرات للحقول عند التركيز
        const formInputs = document.querySelectorAll('.form-control');
        formInputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('input-focused');
            });
            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('input-focused');
            });
        });
        
        // Form validation
        (function() {
            'use strict';
            
            // Fetch all forms that need validation
            var forms = document.querySelectorAll('.needs-validation');
            
            // Loop over them and prevent submission
            Array.prototype.slice.call(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    } else {
                        // إظهار رسالة تحميل عند الإرسال
                        const submitBtn = form.querySelector('button[type="submit"]');
                        const originalText = submitBtn.innerHTML;
                        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> {% trans "جاري الإرسال..." %}';
                        submitBtn.disabled = true;
                        
                        // إعادة النص الأصلي بعد 3 ثوانٍ إذا لم يتم إعادة تحميل الصفحة
                        setTimeout(() => {
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                        }, 3000);
                    }
                    
                    form.classList.add('was-validated');
                }, false);
            });
        })();
        
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // تأثيرات للأيقونات في معلومات الاتصال
        const infoItems = document.querySelectorAll('.contact-info-item');
        infoItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                const icon = this.querySelector('.contact-info-icon');
                icon.style.transform = 'scale(1.1) rotate(10deg)';
            });
            item.addEventListener('mouseleave', function() {
                const icon = this.querySelector('.contact-info-icon');
                icon.style.transform = 'scale(1)';
            });
        });
        
        // إضافة تأثيرات ظهور للعناصر
        const animateElements = document.querySelectorAll('.card, .contact-info-item');
        animateElements.forEach((element, index) => {
            element.classList.add('fadeInUp');
            element.style.animationDelay = (index * 0.1) + 's';
        });
        
        // Initialize Leaflet Map
        try {
            // Create map instance
            var map = L.map('contact-map').setView([18.0735, -15.9582], 13); // نواكشوط، موريتانيا
            
            // Add OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);
            
            // Add marker for the conference location
            var marker = L.marker([18.0735, -15.9582]).addTo(map);
            
            // Add popup to the marker
            marker.bindPopup('<strong>{% trans "المؤتمر السنوي للسيرة النبوية" %}</strong><br>{% trans "نواكشوط، موريتانيا" %}').openPopup();
            
            console.log('Map initialized successfully');
        } catch (error) {
            console.error('Error initializing map:', error);
            document.getElementById('contact-map').innerHTML = '<div class="alert alert-warning">{% trans "تعذر تحميل الخريطة. يرجى التحقق من اتصالك بالإنترنت." %}</div>';
        }
        
        // Add animation to map elements
        setTimeout(function() {
            document.querySelectorAll('.leaflet-marker-icon, .leaflet-popup').forEach(function(el) {
                if (el) el.classList.add('fade-in');
            });
        }, 1000);
        
        // Add animation classes on scroll
        const animateOnScroll = function() {
            const elements = document.querySelectorAll('.slide-up:not(.animated)');
            elements.forEach(function(element) {
                const position = element.getBoundingClientRect().top;
                const windowHeight = window.innerHeight;
                
                if (position < windowHeight * 0.9) {
                    element.classList.add('animated');
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }
            });
        };
        
        // Set initial state for slide-up elements
        document.querySelectorAll('.slide-up').forEach(function(element) {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';
            element.style.transition = 'opacity 0.8s ease-in-out, transform 0.8s ease-in-out';
        });
        
        // Run on load
        animateOnScroll();
        
        // Run on scroll
        window.addEventListener('scroll', animateOnScroll);
    });
</script>
{% endblock %}